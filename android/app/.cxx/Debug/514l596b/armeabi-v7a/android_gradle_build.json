{"buildFiles": ["/home/<USER>/.flutter/flutter/packages/flutter_tools/gradle/src/main/groovy/CMakeLists.txt"], "cleanCommandsComponents": [["/home/<USER>/Android/Sdk/cmake/3.22.1/bin/ninja", "-C", "/home/<USER>/projects/fluttercashbook/android/app/.cxx/Debug/514l596b/armeabi-v7a", "clean"]], "buildTargetsCommandComponents": ["/home/<USER>/Android/Sdk/cmake/3.22.1/bin/ninja", "-C", "/home/<USER>/projects/fluttercashbook/android/app/.cxx/Debug/514l596b/armeabi-v7a", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}, "toolchains": {"toolchain": {"cCompilerExecutable": "/home/<USER>/Android/Sdk/ndk/26.3.11579264/toolchains/llvm/prebuilt/linux-x86_64/bin/clang.lld", "cppCompilerExecutable": "/home/<USER>/Android/Sdk/ndk/26.3.11579264/toolchains/llvm/prebuilt/linux-x86_64/bin/clang++.lld"}}, "cFileExtensions": [], "cppFileExtensions": []}