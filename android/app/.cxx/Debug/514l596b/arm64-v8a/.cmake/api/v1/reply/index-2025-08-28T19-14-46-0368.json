{"cmake": {"generator": {"multiConfig": false, "name": "Ninja"}, "paths": {"cmake": "/home/<USER>/Android/Sdk/cmake/3.22.1/bin/cmake", "cpack": "/home/<USER>/Android/Sdk/cmake/3.22.1/bin/cpack", "ctest": "/home/<USER>/Android/Sdk/cmake/3.22.1/bin/ctest", "root": "/home/<USER>/Android/Sdk/cmake/3.22.1/share/cmake-3.22"}, "version": {"isDirty": false, "major": 3, "minor": 22, "patch": 1, "string": "3.22.1-g37088a8", "suffix": "g37088a8"}}, "objects": [{"jsonFile": "codemodel-v2-76785fb25e188a321dd0.json", "kind": "codemodel", "version": {"major": 2, "minor": 3}}, {"jsonFile": "cache-v2-4368b38d702650c0aaa0.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, {"jsonFile": "cmakeFiles-v1-350d309b9671d548748c.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 0}}], "reply": {"client-agp": {"cache-v2": {"jsonFile": "cache-v2-4368b38d702650c0aaa0.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, "cmakeFiles-v1": {"jsonFile": "cmakeFiles-v1-350d309b9671d548748c.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 0}}, "codemodel-v2": {"jsonFile": "codemodel-v2-76785fb25e188a321dd0.json", "kind": "codemodel", "version": {"major": 2, "minor": 3}}}}}