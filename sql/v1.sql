-- Flutter Cashbook App - Complete Database Migration
-- This script initializes all required tables for the cashbook application
-- Run this migration in your Supabase PostgreSQL database

-- Enable necessary extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pgcrypto";

-- Users table - For user authentication and profiles
CREATE TABLE IF NOT EXISTS users (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    email VARCHAR(255) UNIQUE NOT NULL,
    full_name VARCHAR(255),
    avatar_url TEXT,
    phone VARCHAR(20),
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Businesses table - For business/organization management
CREATE TABLE IF NOT EXISTS businesses (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    name <PERSON><PERSON><PERSON><PERSON>(255) NOT NULL,
    description TEXT,
    logo_url TEXT,
    address TEXT,
    phone VARCHAR(20),
    email VARCHAR(255),
    website VARCHAR(255),
    tax_id VARCHAR(100),
    currency VARCHAR(3) DEFAULT 'USD',
    is_default BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Cashbook templates - Predefined templates for different business types
CREATE TABLE IF NOT EXISTS cashbook_templates (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(255) NOT NULL,
    description TEXT,
    icon VARCHAR(50),
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Cashbooks table - Main cashbook entities
CREATE TABLE IF NOT EXISTS cashbooks (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    business_id UUID REFERENCES businesses(id) ON DELETE CASCADE,
    template_id UUID REFERENCES cashbook_templates(id) ON DELETE SET NULL,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    currency VARCHAR(3) DEFAULT 'USD',
    color VARCHAR(25), -- Hex color code with alpha (0xFF1B365D)
    icon VARCHAR(50),
    balance DECIMAL(15,2) DEFAULT 0.00,
    transaction_count INTEGER DEFAULT 0,
    is_default BOOLEAN DEFAULT FALSE,
    is_active BOOLEAN DEFAULT TRUE,
    last_activity TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Categories table - For transaction categorization
CREATE TABLE IF NOT EXISTS categories (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    template_id UUID REFERENCES cashbook_templates(id) ON DELETE CASCADE,
    name VARCHAR(255) NOT NULL,
    type VARCHAR(20) NOT NULL CHECK (type IN ('income', 'expense')),
    icon VARCHAR(50),
    color VARCHAR(25),
    parent_id UUID REFERENCES categories(id) ON DELETE SET NULL,
    sort_order INTEGER DEFAULT 0,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Payment modes table - Different payment methods
CREATE TABLE IF NOT EXISTS payment_modes (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(255) NOT NULL,
    icon VARCHAR(50),
    color VARCHAR(25),
    is_active BOOLEAN DEFAULT TRUE,
    sort_order INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Transactions table - Main transaction records
CREATE TABLE IF NOT EXISTS transactions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    cashbook_id UUID REFERENCES cashbooks(id) ON DELETE CASCADE,
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    category_id UUID REFERENCES categories(id) ON DELETE SET NULL,
    payment_mode_id UUID REFERENCES payment_modes(id) ON DELETE SET NULL,
    type VARCHAR(20) NOT NULL CHECK (type IN ('income', 'expense')),
    amount DECIMAL(15,2) NOT NULL,
    description TEXT NOT NULL,
    notes TEXT,
    reference_number VARCHAR(100),
    transaction_date DATE NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Transaction attachments table - For receipt images and documents
CREATE TABLE IF NOT EXISTS transaction_attachments (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    transaction_id UUID REFERENCES transactions(id) ON DELETE CASCADE,
    file_name VARCHAR(255) NOT NULL,
    file_path TEXT NOT NULL,
    file_url TEXT NOT NULL,
    file_type VARCHAR(50),
    file_size BIGINT,
    mime_type VARCHAR(100),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- User preferences table
CREATE TABLE IF NOT EXISTS user_preferences (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    theme VARCHAR(20) DEFAULT 'light' CHECK (theme IN ('light', 'dark', 'auto')),
    language VARCHAR(10) DEFAULT 'en',
    default_currency VARCHAR(3) DEFAULT 'USD',
    date_format VARCHAR(20) DEFAULT 'dd/MM/yyyy',
    number_format VARCHAR(20) DEFAULT 'en_US',
    notifications_enabled BOOLEAN DEFAULT TRUE,
    biometric_enabled BOOLEAN DEFAULT FALSE,
    auto_backup BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(user_id)
);

-- Cashbook sharing table - For sharing cashbooks with other users
CREATE TABLE IF NOT EXISTS cashbook_shares (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    cashbook_id UUID REFERENCES cashbooks(id) ON DELETE CASCADE,
    shared_by UUID REFERENCES users(id) ON DELETE CASCADE,
    shared_with UUID REFERENCES users(id) ON DELETE CASCADE,
    permission VARCHAR(20) DEFAULT 'view' CHECK (permission IN ('view', 'edit', 'admin')),
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(cashbook_id, shared_with)
);

-- Budgets table - For budget management
CREATE TABLE IF NOT EXISTS budgets (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    cashbook_id UUID REFERENCES cashbooks(id) ON DELETE CASCADE,
    category_id UUID REFERENCES categories(id) ON DELETE CASCADE,
    name VARCHAR(255) NOT NULL,
    amount DECIMAL(15,2) NOT NULL,
    spent_amount DECIMAL(15,2) DEFAULT 0.00,
    period VARCHAR(20) DEFAULT 'monthly' CHECK (period IN ('weekly', 'monthly', 'quarterly', 'yearly')),
    start_date DATE NOT NULL,
    end_date DATE NOT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Recurring transactions table - For scheduled transactions
CREATE TABLE IF NOT EXISTS recurring_transactions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    cashbook_id UUID REFERENCES cashbooks(id) ON DELETE CASCADE,
    category_id UUID REFERENCES categories(id) ON DELETE SET NULL,
    payment_mode_id UUID REFERENCES payment_modes(id) ON DELETE SET NULL,
    type VARCHAR(20) NOT NULL CHECK (type IN ('income', 'expense')),
    amount DECIMAL(15,2) NOT NULL,
    description TEXT NOT NULL,
    frequency VARCHAR(20) NOT NULL CHECK (frequency IN ('daily', 'weekly', 'monthly', 'quarterly', 'yearly')),
    start_date DATE NOT NULL,
    end_date DATE,
    next_occurrence DATE NOT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_cashbooks_user_id ON cashbooks(user_id);
CREATE INDEX IF NOT EXISTS idx_cashbooks_business_id ON cashbooks(business_id);
CREATE INDEX IF NOT EXISTS idx_transactions_cashbook_id ON transactions(cashbook_id);
CREATE INDEX IF NOT EXISTS idx_transactions_user_id ON transactions(user_id);
CREATE INDEX IF NOT EXISTS idx_transactions_date ON transactions(transaction_date);
CREATE INDEX IF NOT EXISTS idx_transactions_type ON transactions(type);
CREATE INDEX IF NOT EXISTS idx_transaction_attachments_transaction_id ON transaction_attachments(transaction_id);
CREATE INDEX IF NOT EXISTS idx_categories_template_id ON categories(template_id);
CREATE INDEX IF NOT EXISTS idx_categories_type ON categories(type);

-- Insert default cashbook templates
INSERT INTO cashbook_templates (name, description, icon) VALUES
('Retail Shop', 'Perfect for retail businesses with daily sales tracking', 'store'),
('Freelance Work', 'Track project income and business expenses', 'work'),
('Personal Expenses', 'Manage household and personal finances', 'person'),
('Rental Property', 'Track rental income and property expenses', 'home'),
('Restaurant', 'Food service business with inventory tracking', 'restaurant'),
('Consulting', 'Professional services and client billing', 'business'),
('E-commerce', 'Online business sales and expenses', 'shopping_cart'),
('Construction', 'Project-based construction business', 'construction');

-- Insert default categories for each template
-- Retail Shop categories
INSERT INTO categories (template_id, name, type, icon, sort_order) 
SELECT id, 'Sales Revenue', 'income', 'trending_up', 1 FROM cashbook_templates WHERE name = 'Retail Shop'
UNION ALL
SELECT id, 'Product Returns', 'expense', 'keyboard_return', 2 FROM cashbook_templates WHERE name = 'Retail Shop'
UNION ALL
SELECT id, 'Inventory Purchase', 'expense', 'inventory', 3 FROM cashbook_templates WHERE name = 'Retail Shop'
UNION ALL
SELECT id, 'Store Rent', 'expense', 'store', 4 FROM cashbook_templates WHERE name = 'Retail Shop'
UNION ALL
SELECT id, 'Utilities', 'expense', 'flash_on', 5 FROM cashbook_templates WHERE name = 'Retail Shop'
UNION ALL
SELECT id, 'Staff Salaries', 'expense', 'people', 6 FROM cashbook_templates WHERE name = 'Retail Shop';

-- Freelance Work categories
INSERT INTO categories (template_id, name, type, icon, sort_order)
SELECT id, 'Client Payments', 'income', 'payment', 1 FROM cashbook_templates WHERE name = 'Freelance Work'
UNION ALL
SELECT id, 'Project Bonus', 'income', 'star', 2 FROM cashbook_templates WHERE name = 'Freelance Work'
UNION ALL
SELECT id, 'Equipment', 'expense', 'computer', 3 FROM cashbook_templates WHERE name = 'Freelance Work'
UNION ALL
SELECT id, 'Software Subscriptions', 'expense', 'apps', 4 FROM cashbook_templates WHERE name = 'Freelance Work'
UNION ALL
SELECT id, 'Marketing', 'expense', 'campaign', 5 FROM cashbook_templates WHERE name = 'Freelance Work'
UNION ALL
SELECT id, 'Training & Courses', 'expense', 'school', 6 FROM cashbook_templates WHERE name = 'Freelance Work';

-- Personal Expenses categories
INSERT INTO categories (template_id, name, type, icon, sort_order)
SELECT id, 'Salary', 'income', 'account_balance_wallet', 1 FROM cashbook_templates WHERE name = 'Personal Expenses'
UNION ALL
SELECT id, 'Investment Returns', 'income', 'trending_up', 2 FROM cashbook_templates WHERE name = 'Personal Expenses'
UNION ALL
SELECT id, 'Food & Dining', 'expense', 'restaurant', 3 FROM cashbook_templates WHERE name = 'Personal Expenses'
UNION ALL
SELECT id, 'Transportation', 'expense', 'directions_car', 4 FROM cashbook_templates WHERE name = 'Personal Expenses'
UNION ALL
SELECT id, 'Entertainment', 'expense', 'movie', 5 FROM cashbook_templates WHERE name = 'Personal Expenses'
UNION ALL
SELECT id, 'Bills & Utilities', 'expense', 'receipt', 6 FROM cashbook_templates WHERE name = 'Personal Expenses'
UNION ALL
SELECT id, 'Shopping', 'expense', 'shopping_bag', 7 FROM cashbook_templates WHERE name = 'Personal Expenses';

-- Rental Property categories
INSERT INTO categories (template_id, name, type, icon, sort_order)
SELECT id, 'Rent Income', 'income', 'home', 1 FROM cashbook_templates WHERE name = 'Rental Property'
UNION ALL
SELECT id, 'Security Deposit', 'income', 'security', 2 FROM cashbook_templates WHERE name = 'Rental Property'
UNION ALL
SELECT id, 'Maintenance & Repairs', 'expense', 'build', 3 FROM cashbook_templates WHERE name = 'Rental Property'
UNION ALL
SELECT id, 'Property Insurance', 'expense', 'security', 4 FROM cashbook_templates WHERE name = 'Rental Property'
UNION ALL
SELECT id, 'Property Taxes', 'expense', 'account_balance', 5 FROM cashbook_templates WHERE name = 'Rental Property'
UNION ALL
SELECT id, 'Property Management', 'expense', 'business', 6 FROM cashbook_templates WHERE name = 'Rental Property';

-- Insert default payment modes
INSERT INTO payment_modes (name, icon, sort_order) VALUES
('Cash', 'money', 1),
('Credit Card', 'credit_card', 2),
('Debit Card', 'payment', 3),
('Bank Transfer', 'account_balance', 4),
('Digital Wallet', 'account_balance_wallet', 5),
('Check', 'receipt', 6),
('PayPal', 'paypal', 7),
('Cryptocurrency', 'currency_bitcoin', 8),
('Mobile Payment', 'phone_android', 9),
('Wire Transfer', 'swap_horiz', 10);

-- Create triggers to update balances and transaction counts
CREATE OR REPLACE FUNCTION update_cashbook_stats()
RETURNS TRIGGER AS $$
BEGIN
    IF TG_OP = 'INSERT' THEN
        -- Update balance and transaction count for new transaction
        UPDATE cashbooks 
        SET 
            balance = balance + CASE 
                WHEN NEW.type = 'income' THEN NEW.amount 
                ELSE -NEW.amount 
            END,
            transaction_count = transaction_count + 1,
            last_activity = NOW()
        WHERE id = NEW.cashbook_id;
        
        RETURN NEW;
    ELSIF TG_OP = 'UPDATE' THEN
        -- Adjust balance for updated transaction
        UPDATE cashbooks 
        SET 
            balance = balance - CASE 
                WHEN OLD.type = 'income' THEN OLD.amount 
                ELSE -OLD.amount 
            END + CASE 
                WHEN NEW.type = 'income' THEN NEW.amount 
                ELSE -NEW.amount 
            END,
            last_activity = NOW()
        WHERE id = NEW.cashbook_id;
        
        RETURN NEW;
    ELSIF TG_OP = 'DELETE' THEN
        -- Adjust balance and count for deleted transaction
        UPDATE cashbooks 
        SET 
            balance = balance - CASE 
                WHEN OLD.type = 'income' THEN OLD.amount 
                ELSE -OLD.amount 
            END,
            transaction_count = transaction_count - 1,
            last_activity = NOW()
        WHERE id = OLD.cashbook_id;
        
        RETURN OLD;
    END IF;
    
    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

-- Create triggers
CREATE TRIGGER trigger_update_cashbook_stats
    AFTER INSERT OR UPDATE OR DELETE ON transactions
    FOR EACH ROW EXECUTE FUNCTION update_cashbook_stats();

-- Create function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create triggers for updated_at
CREATE TRIGGER trigger_users_updated_at BEFORE UPDATE ON users FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER trigger_businesses_updated_at BEFORE UPDATE ON businesses FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER trigger_cashbooks_updated_at BEFORE UPDATE ON cashbooks FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER trigger_transactions_updated_at BEFORE UPDATE ON transactions FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER trigger_user_preferences_updated_at BEFORE UPDATE ON user_preferences FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER trigger_budgets_updated_at BEFORE UPDATE ON budgets FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER trigger_recurring_transactions_updated_at BEFORE UPDATE ON recurring_transactions FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Enable Row Level Security (RLS) for Supabase
ALTER TABLE users ENABLE ROW LEVEL SECURITY;
ALTER TABLE businesses ENABLE ROW LEVEL SECURITY;
ALTER TABLE cashbooks ENABLE ROW LEVEL SECURITY;
ALTER TABLE transactions ENABLE ROW LEVEL SECURITY;
ALTER TABLE transaction_attachments ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_preferences ENABLE ROW LEVEL SECURITY;
ALTER TABLE cashbook_shares ENABLE ROW LEVEL SECURITY;
ALTER TABLE budgets ENABLE ROW LEVEL SECURITY;
ALTER TABLE recurring_transactions ENABLE ROW LEVEL SECURITY;

-- Create RLS policies
-- Users can only see their own data
CREATE POLICY "Users can view own profile" ON users FOR SELECT USING (auth.uid() = id);
CREATE POLICY "Users can update own profile" ON users FOR UPDATE USING (auth.uid() = id);

-- Businesses policies
CREATE POLICY "Users can view own businesses" ON businesses FOR SELECT USING (auth.uid() = user_id);
CREATE POLICY "Users can insert own businesses" ON businesses FOR INSERT WITH CHECK (auth.uid() = user_id);
CREATE POLICY "Users can update own businesses" ON businesses FOR UPDATE USING (auth.uid() = user_id);
CREATE POLICY "Users can delete own businesses" ON businesses FOR DELETE USING (auth.uid() = user_id);

-- Cashbooks policies
CREATE POLICY "Users can view own cashbooks" ON cashbooks FOR SELECT USING (auth.uid() = user_id);
CREATE POLICY "Users can insert own cashbooks" ON cashbooks FOR INSERT WITH CHECK (auth.uid() = user_id);
CREATE POLICY "Users can update own cashbooks" ON cashbooks FOR UPDATE USING (auth.uid() = user_id);
CREATE POLICY "Users can delete own cashbooks" ON cashbooks FOR DELETE USING (auth.uid() = user_id);

-- Transactions policies
CREATE POLICY "Users can view own transactions" ON transactions FOR SELECT USING (auth.uid() = user_id);
CREATE POLICY "Users can insert own transactions" ON transactions FOR INSERT WITH CHECK (auth.uid() = user_id);
CREATE POLICY "Users can update own transactions" ON transactions FOR UPDATE USING (auth.uid() = user_id);
CREATE POLICY "Users can delete own transactions" ON transactions FOR DELETE USING (auth.uid() = user_id);

-- Transaction attachments policies
CREATE POLICY "Users can view own transaction attachments" ON transaction_attachments FOR SELECT 
USING (EXISTS (SELECT 1 FROM transactions WHERE transactions.id = transaction_attachments.transaction_id AND transactions.user_id = auth.uid()));

CREATE POLICY "Users can insert own transaction attachments" ON transaction_attachments FOR INSERT 
WITH CHECK (EXISTS (SELECT 1 FROM transactions WHERE transactions.id = transaction_attachments.transaction_id AND transactions.user_id = auth.uid()));

CREATE POLICY "Users can update own transaction attachments" ON transaction_attachments FOR UPDATE 
USING (EXISTS (SELECT 1 FROM transactions WHERE transactions.id = transaction_attachments.transaction_id AND transactions.user_id = auth.uid()));

CREATE POLICY "Users can delete own transaction attachments" ON transaction_attachments FOR DELETE 
USING (EXISTS (SELECT 1 FROM transactions WHERE transactions.id = transaction_attachments.transaction_id AND transactions.user_id = auth.uid()));

-- User preferences policies
CREATE POLICY "Users can view own preferences" ON user_preferences FOR SELECT USING (auth.uid() = user_id);
CREATE POLICY "Users can insert own preferences" ON user_preferences FOR INSERT WITH CHECK (auth.uid() = user_id);
CREATE POLICY "Users can update own preferences" ON user_preferences FOR UPDATE USING (auth.uid() = user_id);

-- Budgets policies
CREATE POLICY "Users can view own budgets" ON budgets FOR SELECT 
USING (EXISTS (SELECT 1 FROM cashbooks WHERE cashbooks.id = budgets.cashbook_id AND cashbooks.user_id = auth.uid()));

CREATE POLICY "Users can insert own budgets" ON budgets FOR INSERT 
WITH CHECK (EXISTS (SELECT 1 FROM cashbooks WHERE cashbooks.id = budgets.cashbook_id AND cashbooks.user_id = auth.uid()));

CREATE POLICY "Users can update own budgets" ON budgets FOR UPDATE 
USING (EXISTS (SELECT 1 FROM cashbooks WHERE cashbooks.id = budgets.cashbook_id AND cashbooks.user_id = auth.uid()));

CREATE POLICY "Users can delete own budgets" ON budgets FOR DELETE 
USING (EXISTS (SELECT 1 FROM cashbooks WHERE cashbooks.id = budgets.cashbook_id AND cashbooks.user_id = auth.uid()));

-- Recurring transactions policies
CREATE POLICY "Users can view own recurring transactions" ON recurring_transactions FOR SELECT 
USING (EXISTS (SELECT 1 FROM cashbooks WHERE cashbooks.id = recurring_transactions.cashbook_id AND cashbooks.user_id = auth.uid()));

CREATE POLICY "Users can insert own recurring transactions" ON recurring_transactions FOR INSERT 
WITH CHECK (EXISTS (SELECT 1 FROM cashbooks WHERE cashbooks.id = recurring_transactions.cashbook_id AND cashbooks.user_id = auth.uid()));

CREATE POLICY "Users can update own recurring transactions" ON recurring_transactions FOR UPDATE 
USING (EXISTS (SELECT 1 FROM cashbooks WHERE cashbooks.id = recurring_transactions.cashbook_id AND cashbooks.user_id = auth.uid()));

CREATE POLICY "Users can delete own recurring transactions" ON recurring_transactions FOR DELETE 
USING (EXISTS (SELECT 1 FROM cashbooks WHERE cashbooks.id = recurring_transactions.cashbook_id AND cashbooks.user_id = auth.uid()));

-- Allow public read access to templates, categories, and payment modes
ALTER TABLE cashbook_templates ENABLE ROW LEVEL SECURITY;
ALTER TABLE categories ENABLE ROW LEVEL SECURITY;
ALTER TABLE payment_modes ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Anyone can view templates" ON cashbook_templates FOR SELECT USING (true);
CREATE POLICY "Anyone can view categories" ON categories FOR SELECT USING (true);
CREATE POLICY "Anyone can view payment modes" ON payment_modes FOR SELECT USING (true);