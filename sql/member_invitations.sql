-- Member Invitations System - Database Migration
-- This script adds tables for business member invitations and roles

-- Business members table - For managing business team members
CREATE TABLE IF NOT EXISTS business_members (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    business_id UUID REFERENCES businesses(id),
    user_id UUID REFERENCES users(id),
    role VARCHAR(20) DEFAULT 'staff' CHECK (role IN ('partner', 'admin', 'staff')),
    invited_by UUID REFERENCES users(id),
    joined_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(business_id, user_id)
);

-- Business invitations table - For managing pending invitations
CREATE TABLE IF NOT EXISTS business_invitations (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    business_id UUID REFERENCES businesses(id) ON DELETE CASCADE,
    email VARCHAR(255) NOT NULL,
    role VARCHAR(20) DEFAULT 'staff' CHECK (role IN ('partner', 'admin', 'staff')),
    invited_by UUID REFERENCES users(id) ON DELETE CASCADE,
    invitation_token VARCHAR(255) UNIQUE NOT NULL,
    status VARCHAR(20) DEFAULT 'pending' CHECK (status IN ('pending', 'accepted', 'declined', 'expired')),
    expires_at TIMESTAMP WITH TIME ZONE DEFAULT (NOW() + INTERVAL '7 days'),
    accepted_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(business_id, email)
);

-- Member cashbook permissions table - For assigning cashbook access to members
CREATE TABLE IF NOT EXISTS member_cashbook_permissions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    business_member_id UUID REFERENCES business_members(id),
    cashbook_id UUID REFERENCES cashbooks(id),
    user_id UUID REFERENCES users(id),
    permission VARCHAR(20) DEFAULT 'read' CHECK (permission IN ('read', 'write')),
    granted_by UUID REFERENCES users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(business_member_id, cashbook_id)
);

-- Enable Row Level Security
ALTER TABLE business_members ENABLE ROW LEVEL SECURITY;
ALTER TABLE business_invitations ENABLE ROW LEVEL SECURITY;
ALTER TABLE member_cashbook_permissions ENABLE ROW LEVEL SECURITY;

-- RLS Policies for business_members
CREATE POLICY "Business owners can manage members" ON business_members 
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM businesses 
            WHERE businesses.id = business_members.business_id 
            AND businesses.user_id = auth.uid()
        )
    );

CREATE POLICY "Members can view their own membership" ON business_members 
    FOR SELECT USING (user_id = auth.uid());

-- RLS Policies for business_invitations
CREATE POLICY "Business owners can manage invitations" ON business_invitations 
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM businesses 
            WHERE businesses.id = business_invitations.business_id 
            AND businesses.user_id = auth.uid()
        )
    );

-- RLS Policies for member_cashbook_permissions
CREATE POLICY "Business owners can manage permissions" ON member_cashbook_permissions 
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM business_members bm
            JOIN businesses b ON b.id = bm.business_id
            WHERE bm.id = member_cashbook_permissions.business_member_id 
            AND b.user_id = auth.uid()
        )
    );

CREATE POLICY "Members can view their own permissions" ON member_cashbook_permissions 
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM business_members bm
            WHERE bm.id = member_cashbook_permissions.business_member_id 
            AND bm.user_id = auth.uid()
        )
    );

-- Indexes for better performance
CREATE INDEX IF NOT EXISTS idx_business_members_business_id ON business_members(business_id);
CREATE INDEX IF NOT EXISTS idx_business_members_user_id ON business_members(user_id);
CREATE INDEX IF NOT EXISTS idx_business_invitations_business_id ON business_invitations(business_id);
CREATE INDEX IF NOT EXISTS idx_business_invitations_email ON business_invitations(email);
CREATE INDEX IF NOT EXISTS idx_business_invitations_token ON business_invitations(invitation_token);
CREATE INDEX IF NOT EXISTS idx_business_invitations_status ON business_invitations(status);
CREATE INDEX IF NOT EXISTS idx_member_permissions_member_id ON member_cashbook_permissions(business_member_id);
CREATE INDEX IF NOT EXISTS idx_member_permissions_cashbook_id ON member_cashbook_permissions(cashbook_id);

-- Function to generate invitation tokens
CREATE OR REPLACE FUNCTION generate_invitation_token() RETURNS TEXT AS $$
BEGIN
    RETURN encode(gen_random_bytes(32), 'base64');
END;
$$ LANGUAGE plpgsql;

-- Function to automatically expire old invitations
CREATE OR REPLACE FUNCTION expire_old_invitations() RETURNS void AS $$
BEGIN
    UPDATE business_invitations 
    SET status = 'expired', updated_at = NOW()
    WHERE status = 'pending' AND expires_at < NOW();
END;
$$ LANGUAGE plpgsql;

-- Create a trigger to automatically set invitation tokens
CREATE OR REPLACE FUNCTION set_invitation_token() RETURNS TRIGGER AS $$
BEGIN
    IF NEW.invitation_token IS NULL OR NEW.invitation_token = '' THEN
        NEW.invitation_token := generate_invitation_token();
    END IF;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_set_invitation_token
    BEFORE INSERT ON business_invitations
    FOR EACH ROW
    EXECUTE FUNCTION set_invitation_token();
