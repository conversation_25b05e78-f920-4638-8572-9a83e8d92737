import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';

import '../../../core/app_export.dart';

class DashboardHeaderWidget extends StatelessWidget {
  final String userName;
  final bool isOnline;
  final VoidCallback onNotificationTap;
  final VoidCallback onProfileTap;

  const DashboardHeaderWidget({
    super.key,
    required this.userName,
    required this.isOnline,
    required this.onNotificationTap,
    required this.onProfileTap,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    return Container(
      width: double.infinity,
      padding: EdgeInsets.symmetric(horizontal: 5.w, vertical: 2.h),
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        boxShadow: [
          BoxShadow(
            color: isDark
                ? Colors.white.withValues(alpha: 0.02)
                : Colors.black.withValues(alpha: 0.04),
            offset: const Offset(0, 2),
            blurRadius: 8,
            spreadRadius: 0,
          ),
        ],
      ),
      child: <PERSON><PERSON><PERSON>(
        child: Row(
          children: [
            // App Logo and Brand
            Container(
              width: 10.w,
              height: 10.w,
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [
                    theme.colorScheme.primary,
                    theme.colorScheme.primary.withValues(alpha: 0.7),
                  ],
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                ),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Center(
                child: CustomIconWidget(
                  iconName: 'account_balance_wallet',
                  color: Colors.white,
                  size: 24,
                ),
              ),
            ),
            SizedBox(width: 3.w),

            // App Name and User Greeting
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Dashboard',
                    style: theme.textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.w700,
                      color: theme.colorScheme.primary,
                      fontSize: 16.sp,
                    ),
                  ),
                  SizedBox(height: 0.5.h),
                  // Row(
                  //   children: [
                  //     Text(
                  //       'Hello, $userName',
                  //       style: theme.textTheme.bodyMedium?.copyWith(
                  //         color: theme.colorScheme.onSurface
                  //             .withValues(alpha: 0.7),
                  //         fontWeight: FontWeight.w400,
                  //       ),
                  //       overflow: TextOverflow.ellipsis,
                  //     ),
                  //     SizedBox(width: 2.w),
                  //     Container(
                  //       width: 8,
                  //       height: 8,
                  //       decoration: BoxDecoration(
                  //         color: isOnline
                  //             ? AppTheme.getSuccessColor(isDark)
                  //             : AppTheme.getWarningColor(isDark),
                  //         shape: BoxShape.circle,
                  //       ),
                  //     ),
                  //   ],
                  // ),
                ],
              ),
            ),

            // Notification Button
            GestureDetector(
              onTap: onNotificationTap,
              child: Container(
                width: 10.w,
                height: 10.w,
                decoration: BoxDecoration(
                  color: theme.colorScheme.primary.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Stack(
                  children: [
                    Center(
                      child: CustomIconWidget(
                        iconName: 'notifications_outlined',
                        color: theme.colorScheme.primary,
                        size: 20,
                      ),
                    ),
                    Positioned(
                      top: 1.5.w,
                      right: 1.5.w,
                      child: Container(
                        width: 8,
                        height: 8,
                        decoration: BoxDecoration(
                          color: AppTheme.getErrorColor(isDark),
                          shape: BoxShape.circle,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),

            SizedBox(width: 3.w),

            // Profile Button
            GestureDetector(
              onTap: onProfileTap,
              child: Container(
                width: 10.w,
                height: 10.w,
                decoration: BoxDecoration(
                  color: theme.colorScheme.secondary.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Center(
                  child: CustomIconWidget(
                    iconName: 'account_circle_outlined',
                    color: theme.colorScheme.secondary,
                    size: 20,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
