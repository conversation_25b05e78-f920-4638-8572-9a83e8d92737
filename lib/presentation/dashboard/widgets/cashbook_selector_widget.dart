import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';

import '../../../core/app_export.dart';
import '../../../widgets/custom_icon_widget.dart';

class CashbookSelectorWidget extends StatelessWidget {
  final String selectedCashbook;
  final List<Map<String, dynamic>> cashbooks;
  final ValueChanged<String> onCashbookChanged;

  const CashbookSelectorWidget({
    super.key,
    required this.selectedCashbook,
    required this.cashbooks,
    required this.onCashbookChanged,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    return Container(
      width: 90.w,
      margin: EdgeInsets.symmetric(horizontal: 5.w, vertical: 1.h),
      padding: EdgeInsets.symmetric(horizontal: 4.w, vertical: 2.h),
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: theme.colorScheme.outline.withValues(alpha: 0.2),
          width: 1,
        ),
      ),
      child: Row(
        children: [
          CustomIconWidget(
            iconName: 'account_balance_wallet',
            color: theme.colorScheme.primary,
            size: 20,
          ),
          SizedBox(width: 3.w),
          Expanded(
            child: DropdownButtonHideUnderline(
              child: DropdownButton<String>(
                value: selectedCashbook,
                isExpanded: true,
                icon: CustomIconWidget(
                  iconName: 'keyboard_arrow_down',
                  color: theme.colorScheme.onSurface.withValues(alpha: 0.6),
                  size: 20,
                ),
                style: theme.textTheme.titleMedium?.copyWith(
                  color: theme.colorScheme.onSurface,
                  fontWeight: FontWeight.w500,
                ),
                dropdownColor: theme.colorScheme.surface,
                borderRadius: BorderRadius.circular(12),
                items: cashbooks.map<DropdownMenuItem<String>>((cashbook) {
                  return DropdownMenuItem<String>(
                    value: cashbook['id'] as String,
                    child: Row(
                      children: [
                        Container(
                          width: 8,
                          height: 8,
                          decoration: BoxDecoration(
                            color: Color(cashbook['color'] as int),
                            shape: BoxShape.circle,
                          ),
                        ),
                        SizedBox(width: 3.w),
                        Expanded(
                          child: Text(
                            cashbook['name'] as String,
                            style: theme.textTheme.bodyMedium?.copyWith(
                              color: theme.colorScheme.onSurface,
                            ),
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                      ],
                    ),
                  );
                }).toList(),
                onChanged: (String? newValue) {
                  if (newValue != null) {
                    onCashbookChanged(newValue);
                  }
                },
              ),
            ),
          ),
          GestureDetector(
            onTap: () {
              Navigator.pushNamed(context, '/cashbook-management');
            },
            child: Container(
              padding: EdgeInsets.all(1.5.w),
              // decoration: BoxDecoration(
              //   color: theme.colorScheme.primary.withValues(alpha: 0.1),
              //   borderRadius: BorderRadius.circular(8),
              // ),
              // child: CustomIconWidget(
              //   iconName: 'settings',
              //   color: theme.colorScheme.primary,
              //   size: 16,
              // ),
            ),
          ),
        ],
      ),
    );
  }
}
