import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';

import '../../../core/app_export.dart';

class QuickActionFabWidget extends StatefulWidget {
  final VoidCallback onAddIncome;
  final VoidCallback onAddExpense;

  const QuickActionFabWidget({
    super.key,
    required this.onAddIncome,
    required this.onAddExpense,
  });

  @override
  State<QuickActionFabWidget> createState() => _QuickActionFabWidgetState();
}

class _QuickActionFabWidgetState extends State<QuickActionFabWidget>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _animation;
  bool _isExpanded = false;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _animation = CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    );
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _toggleExpansion() {
    setState(() {
      _isExpanded = !_isExpanded;
      if (_isExpanded) {
        _animationController.forward();
      } else {
        _animationController.reverse();
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Stack(
      alignment: Alignment.bottomRight,
      children: [
        // Backdrop
        if (_isExpanded)
          GestureDetector(
            onTap: _toggleExpansion,
            child: Container(
              width: 100.w,
              height: 100.h,
              color: Colors.black.withValues(alpha: 0.3),
            ),
          ),

        // Action buttons
        // Replace your AnimatedBuilder with this
        AnimatedBuilder(
          animation: _animation,
          builder: (context, child) {
            return Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                // Add Income button
                IgnorePointer(
                  ignoring:
                      _animation.value < 0.5, // Ignore when not fully visible
                  child: Transform.scale(
                    scale: _animation.value,
                    child: Transform.translate(
                      offset: Offset(0, -_animation.value * 9),
                      child: Opacity(
                        opacity: _animation.value,
                        child: _buildActionButton(
                          context,
                          'Add Income',
                          AppTheme.getSuccessColor(
                              theme.brightness == Brightness.dark),
                          'add_circle',
                          widget.onAddIncome,
                        ),
                      ),
                    ),
                  ),
                ),
                // Add Expense button
                IgnorePointer(
                  ignoring: _animation.value < 0.5,
                  child: Transform.scale(
                    scale: _animation.value,
                    child: Transform.translate(
                      offset: Offset(0, -_animation.value * 0),
                      child: Opacity(
                        opacity: _animation.value,
                        child: _buildActionButton(
                          context,
                          'Add Expense',
                          AppTheme.getErrorColor(
                              theme.brightness == Brightness.dark),
                          'remove_circle',
                          widget.onAddExpense,
                        ),
                      ),
                    ),
                  ),
                ),
                SizedBox(height: 2.h),
                // Main FAB (unchanged)
                FloatingActionButton(
                  onPressed: _toggleExpansion,
                  backgroundColor: theme.colorScheme.primary,
                  foregroundColor: theme.colorScheme.onPrimary,
                  elevation: 4,
                  child: AnimatedRotation(
                    turns: _isExpanded ? 0.125 : 0,
                    duration: const Duration(milliseconds: 300),
                    child: CustomIconWidget(
                      iconName: 'add',
                      color: theme.colorScheme.onPrimary,
                      size: 28,
                    ),
                  ),
                ),
              ],
            );
          },
        ),
      ],
    );
  }

  Widget _buildActionButton(
    BuildContext context,
    String label,
    Color color,
    String iconName,
    VoidCallback onPressed,
  ) {
    final theme = Theme.of(context);

    return Container(
      margin: EdgeInsets.only(bottom: 1.h, right: 2.w),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            padding: EdgeInsets.symmetric(horizontal: 3.w, vertical: 1.h),
            decoration: BoxDecoration(
              color: theme.colorScheme.surface,
              borderRadius: BorderRadius.circular(20),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.1),
                  offset: const Offset(0, 2),
                  blurRadius: 8,
                ),
              ],
            ),
            child: Text(
              label,
              style: theme.textTheme.bodyMedium?.copyWith(
                color: theme.colorScheme.onSurface,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          SizedBox(width: 3.w),
          FloatingActionButton.small(
            onPressed: () {
              _toggleExpansion();
              print('small pressed ===========================');
              onPressed();
            },
            backgroundColor: color,
            foregroundColor: Colors.white,
            elevation: 2,
            child: CustomIconWidget(
              iconName: iconName,
              color: Colors.white,
              size: 20,
            ),
          ),
        ],
      ),
    );
  }
}
