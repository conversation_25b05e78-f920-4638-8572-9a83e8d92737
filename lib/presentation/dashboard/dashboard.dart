import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';

import '../../core/app_export.dart';
import './widgets/balance_card_widget.dart';
import './widgets/cashbook_selector_widget.dart';
import './widgets/dashboard_header_widget.dart';
import './widgets/quick_action_fab_widget.dart';
import './widgets/quick_filter_chips_widget.dart';
import './widgets/recent_transactions_widget.dart';

class Dashboard extends StatefulWidget {
  const Dashboard({super.key});

  @override
  State<Dashboard> createState() => _DashboardState();
}

class _DashboardState extends State<Dashboard> with TickerProviderStateMixin {
  // State variables
  bool _isBalanceVisible = true;
  String? _selectedCashbookId;
  String _selectedFilter = 'All';
  bool _isOnline = true;
  int _currentBottomNavIndex = 0;
  bool _isLoading = true;
  String? _error;
  List<CategoryModel>? _categories;
  List<dynamic> _permissions = [];

  // Data
  List<CashbookModel> _cashbooks = [];
  List<TransactionModel> _allTransactions = [];
  UserModel? _currentUser;
  CashbookModel? _defaultCashbook;

  // Services
  late DataService _dataService;
  late AuthService _authService;

  @override
  void initState() {
    super.initState();
    _dataService = DataService.instance;
    _authService = AuthService.instance;
    _loadCategories();
    _loadDashboardData();
    _checkConnectivity();
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    _handleNavigationArguments();
  }

  Future<void> _loadPermissions() async {
    if (_selectedCashbookId == null) return;

    List<dynamic> permissions =
        await _dataService.getUserCashbookPermissions(_selectedCashbookId!);

    setState(() {
      _permissions = permissions;
    });
  }

  void _handleNavigationArguments() async {
    final args =
        ModalRoute.of(context)?.settings.arguments as Map<String, dynamic>;
    if (_selectedCashbookId != null) return;
    _selectedCashbookId =
        ((args.isNotEmpty) ? args['cashbook_id'] as String? : null);
  }

  Future<void> _loadCategories() async {
    List<CategoryModel> categories = await _dataService.getAllCategories();
    setState(() {
      _categories = categories;
    });
  }

  String get _currency {
    return _currentCashbook?.currency ?? 'USD';
  }

  Future<void> _loadDashboardData() async {
    if (!_authService.isAuthenticated) {
      Navigator.pushReplacementNamed(context, '/login');
      return;
    }

    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      // Load user profile
      _currentUser = await _authService.getCurrentUserProfile();
      // Load dashboard data
      final dashboardData =
          await _dataService.getDashboardData(_selectedCashbookId!);

      await _loadPermissions();

      setState(() {
        _cashbooks = dashboardData['cashbooks'] as List<CashbookModel>;
        _allTransactions =
            dashboardData['recent_transactions'] as List<TransactionModel>;
        _defaultCashbook = dashboardData['default_cashbook'] as CashbookModel?;

        // Set selected cashbook to default or first available
        if (_defaultCashbook != null) {
          _selectedCashbookId = _defaultCashbook!.id;
        } else if (_cashbooks.isNotEmpty) {
          _selectedCashbookId = _cashbooks.first.id;
        }

        _isLoading = false;
      });
    } catch (error) {
      setState(() {
        _error = error.toString();
        _isLoading = false;
      });
    }
  }

  void _checkConnectivity() {
    // TODO: Implement actual connectivity check
    setState(() {
      _isOnline = true;
    });
  }

  CashbookModel? get _currentCashbook {
    if (_selectedCashbookId == null || _cashbooks.isEmpty) return null;

    try {
      return _cashbooks.firstWhere(
        (cashbook) => cashbook.id == _selectedCashbookId,
      );
    } catch (e) {
      return _cashbooks.isNotEmpty ? _cashbooks.first : null;
    }
  }

  List<TransactionModel> get _filteredTransactions {
    if (_selectedCashbookId == null) return [];

    final now = DateTime.now();
    final cashbookTransactions = _allTransactions
        .where((txn) => txn.cashbookId == _selectedCashbookId)
        .toList();

    switch (_selectedFilter) {
      case 'Today':
        return cashbookTransactions.where((txn) {
          final date = txn.transactionDate;
          return date.day == now.day &&
              date.month == now.month &&
              date.year == now.year;
        }).toList();
      case 'Week':
        final weekStart = now.subtract(Duration(days: now.weekday - 1));
        return cashbookTransactions.where((txn) {
          final date = txn.transactionDate;
          return date.isAfter(weekStart.subtract(const Duration(days: 1)));
        }).toList();
      case 'Month':
        return cashbookTransactions.where((txn) {
          final date = txn.transactionDate;
          return date.month == now.month && date.year == now.year;
        }).toList();
      default:
        return cashbookTransactions;
    }
  }

  // Convert TransactionModel to Map for widget compatibility
  List<Map<String, dynamic>> get _filteredTransactionsAsMap {
    return _filteredTransactions
        .map((txn) => {
              ...txn.toJson(),
              'id': txn.id,
              'type': txn.type.name,
              'amount': txn.amount,
              'category':
                  _categories?.firstWhere((c) => c.id == txn.categoryId).name ??
                      'General',
              'categoryIcon':
                  _categories?.firstWhere((c) => c.id == txn.categoryId).icon ??
                      'category',
              'description': txn.description,
              'date': txn.transactionDate,
              'cashbookId': txn.cashbookId,
              'permissions': _permissions
            })
        .toList();
  }

  // Convert CashbookModel to Map for widget compatibility
  List<Map<String, dynamic>> get _cashbooksAsMap {
    return _cashbooks
        .map((cashbook) => {
              ...cashbook.toJson(),
              'id': cashbook.id,
              'name': cashbook.name,
              'color': cashbook.color != null
                  ? int.tryParse(cashbook.color!) ?? 0xFF1B365D
                  : 0xFF1B365D,
              'balance': cashbook.balance,
              'cashIn': _allTransactions
                  .where((txn) => txn.cashbookId == cashbook.id)
                  .where((txn) => txn.type == TransactionType.income)
                  .fold(0.0, (sum, txn) => sum + txn.amount),
              'cashOut': _allTransactions
                  .where((txn) => txn.cashbookId == cashbook.id)
                  .where((txn) => txn.type == TransactionType.expense)
                  .fold(0.0, (sum, txn) => sum + txn.amount)
            })
        .toList();
  }

  void _toggleBalanceVisibility() {
    setState(() {
      _isBalanceVisible = !_isBalanceVisible;
    });
  }

  void _onCashbookChanged(String cashbookId) {
    setState(() {
      _selectedCashbookId = cashbookId;
    });
    _loadTransactionsForCashbook(cashbookId);
    _loadDashboardData();
  }

  Future<void> _loadTransactionsForCashbook(String cashbookId) async {
    try {
      final transactions = await _dataService.getCashbookTransactions(
        cashbookId: cashbookId,
        limit: 20,
      );

      setState(() {
        _allTransactions = transactions;
      });
    } catch (error) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Failed to load transactions: $error')),
      );
    }
  }

  void _onFilterChanged(String filter) {
    setState(() {
      _selectedFilter = filter;
    });
  }

  Future<void> _onRefresh() async {
    await _loadDashboardData();
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Data refreshed successfully'),
        duration: Duration(seconds: 2),
      ),
    );
  }

  void _onEditTransaction(TransactionModel transaction) {
    Navigator.pushNamed(context, '/add-transaction', arguments: {
      'transactionId': transaction.id,
      'mode': 'edit',
    });
  }

  Future<void> _onDeleteTransaction(TransactionModel transaction) async {
    try {
      await _dataService.deleteTransaction(transaction.id);
      await _loadDashboardData(); // Refresh data
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Transaction deleted successfully')),
      );
    } catch (error) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Failed to delete transaction: $error')),
      );
    }
  }

  void _onAddIncome() {
    Navigator.pushNamed(context, '/add-transaction', arguments: {
      'type': 'income',
      'cashbookId': _selectedCashbookId,
      'mode': 'add'
    });
  }

  void _onAddExpense() {
    Navigator.pushNamed(context, '/add-transaction', arguments: {
      'type': 'expense',
      'cashbookId': _selectedCashbookId,
      'mode': 'add'
    });
  }

  void _onNotificationTap() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Notifications feature coming soon')),
    );
  }

  void _onProfileTap() {
    Navigator.pushNamed(context, '/settings');
  }

  void _onBottomNavTap(int index) {
    if (!_permissions.contains('write')) index += 1;

    setState(() {
      if (index == 1) return;
    });

    switch (index) {
      case 0:
        // Already on dashboard
        break;
      case 1:
        Navigator.pushNamed(context, '/add-transaction',
            arguments: {'cashbookId': _selectedCashbookId, 'mode': 'add'});
        break;
      case 2:
        Navigator.pushNamed(context, '/transaction-history',
            arguments: {'cashbook_id': _selectedCashbookId});
        break;
      case 3:
        Navigator.pushNamed(context, '/reports',
            arguments: {'cashbook_id': _selectedCashbookId});
        break;
    }
  }

  List<BottomNavigationBarItem> _buildBottomNavItems() {
    var items = [
      BottomNavigationBarItem(
        icon: Icon(Icons.dashboard_outlined),
        activeIcon: Icon(Icons.dashboard),
        label: 'Dashboard',
      ),
      BottomNavigationBarItem(
        icon: Icon(Icons.history_outlined),
        activeIcon: Icon(Icons.history),
        label: 'History',
      ),
      BottomNavigationBarItem(
        icon: Icon(Icons.bar_chart_outlined),
        activeIcon: Icon(Icons.bar_chart),
        label: 'Reports',
      ),
    ];

    if (_permissions.contains('write')) {
      items.insert(
          1,
          BottomNavigationBarItem(
            icon: Icon(Icons.add_circle_outline),
            activeIcon: Icon(Icons.add_circle),
            label: 'Add',
          ));
    }

    return items;
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    // Show loading state
    if (_isLoading) {
      return Scaffold(
        backgroundColor: theme.scaffoldBackgroundColor,
        body: const Center(
          child: CircularProgressIndicator(),
        ),
      );
    }

    // Show error state
    if (_error != null) {
      return Scaffold(
        backgroundColor: theme.scaffoldBackgroundColor,
        body: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.error_outline,
                size: 64,
                color: theme.colorScheme.error,
              ),
              SizedBox(height: 2.h),
              Text(
                'Failed to load dashboard',
                style: theme.textTheme.headlineSmall,
              ),
              SizedBox(height: 1.h),
              Text(
                _error!,
                style: theme.textTheme.bodyMedium,
                textAlign: TextAlign.center,
              ),
              SizedBox(height: 3.h),
              ElevatedButton(
                onPressed: _loadDashboardData,
                child: const Text('Retry'),
              ),
            ],
          ),
        ),
      );
    }

    // Show empty state if no cashbooks
    if (_cashbooks.isEmpty) {
      return Scaffold(
        backgroundColor: theme.scaffoldBackgroundColor,
        body: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.account_balance_wallet_outlined,
                size: 64,
                color: theme.colorScheme.primary,
              ),
              SizedBox(height: 2.h),
              Text(
                'No Cashbooks Found',
                style: theme.textTheme.headlineSmall,
              ),
              SizedBox(height: 1.h),
              Text(
                'Create your first cashbook to get started',
                style: theme.textTheme.bodyMedium,
                textAlign: TextAlign.center,
              ),
              SizedBox(height: 3.h),
              ElevatedButton(
                onPressed: () =>
                    Navigator.pushNamed(context, '/cashbook-management'),
                child: const Text('Create Cashbook'),
              ),
            ],
          ),
        ),
      );
    }

    final currentCashbook = _currentCashbook;
    if (currentCashbook == null) {
      return const Scaffold(
        body: Center(child: Text('No cashbook selected')),
      );
    }

    return Scaffold(
      backgroundColor: theme.scaffoldBackgroundColor,
      body: Column(
        children: [
          // Header
          DashboardHeaderWidget(
            userName: _currentUser?.fullName ?? 'User',
            isOnline: _isOnline,
            onNotificationTap: _onNotificationTap,
            onProfileTap: _onProfileTap,
          ),

          // Main Content
          Expanded(
            child: RefreshIndicator(
              onRefresh: _onRefresh,
              child: SingleChildScrollView(
                physics: const AlwaysScrollableScrollPhysics(),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Cashbook Selector
                    CashbookSelectorWidget(
                      selectedCashbook: _selectedCashbookId ?? '',
                      cashbooks: _cashbooksAsMap,
                      onCashbookChanged: _onCashbookChanged,
                    ),

                    // Balance Card
                    BalanceCardWidget(
                      cashIn: _allTransactions
                          .where((txn) =>
                              txn.type == TransactionType.income &&
                              txn.cashbookId == _selectedCashbookId)
                          .fold(0.0, (sum, txn) => sum + txn.amount),
                      cashOut: _allTransactions
                          .where((txn) =>
                              txn.type == TransactionType.expense &&
                              txn.cashbookId == _selectedCashbookId)
                          .fold(0.0, (sum, txn) => sum + txn.amount),
                      netBalance: currentCashbook.balance,
                      isBalanceVisible: _isBalanceVisible,
                      onToggleVisibility: _toggleBalanceVisibility,
                      currency: _currency,
                    ),

                    SizedBox(height: 1.h),

                    // Quick Filter Chips
                    QuickFilterChipsWidget(
                      selectedFilter: _selectedFilter,
                      onFilterChanged: _onFilterChanged,
                    ),

                    SizedBox(height: 2.h),

                    // Recent Transactions
                    RecentTransactionsWidget(
                      cashbookId: currentCashbook.id,
                      transactions: _filteredTransactionsAsMap,
                      permissions: _permissions,
                      onRefresh: _onRefresh,
                      onEditTransaction: _onEditTransaction,
                      onDeleteTransaction: _onDeleteTransaction,
                      currency: _currency,
                    ),

                    SizedBox(height: 10.h), // Space for FAB
                  ],
                ),
              ),
            ),
          ),
        ],
      ),

      // Bottom Navigation Bar
      bottomNavigationBar: BottomNavigationBar(
        currentIndex: _currentBottomNavIndex,
        onTap: _onBottomNavTap,
        type: BottomNavigationBarType.fixed,
        backgroundColor: theme.colorScheme.surface,
        selectedItemColor: theme.colorScheme.primary,
        unselectedItemColor: theme.colorScheme.onSurface.withValues(alpha: 0.6),
        elevation: 8,
        items: _buildBottomNavItems(),
      ),

      // Floating Action Button
      floatingActionButton: _permissions.contains('write')
          ? QuickActionFabWidget(
              onAddIncome: _onAddIncome, onAddExpense: _onAddExpense)
          : null,
    );
  }
}
