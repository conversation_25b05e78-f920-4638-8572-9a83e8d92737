import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';

import '../../../core/app_export.dart';

class MemberCardWidget extends StatefulWidget {
  final BusinessMemberModel member;
  final bool canManage;
  final Function(MemberRole) onUpdateRole;
  final VoidCallback onRemove;
  final VoidCallback onManagePermissions;
  final bool isBusinessCreator;

  const MemberCardWidget({
    super.key,
    required this.member,
    required this.canManage,
    required this.onUpdateRole,
    required this.onRemove,
    required this.onManagePermissions,
    this.isBusinessCreator = false,
  });

  @override
  State<MemberCardWidget> createState() => _MemberCardWidgetState();
}

class _MemberCardWidgetState extends State<MemberCardWidget>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  late Animation<double> _fadeAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    _scaleAnimation = Tween<double>(
      begin: 0.95,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOutBack,
    ));

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOut,
    ));

    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;
    final colorScheme = theme.colorScheme;

    return AnimatedBuilder(
      animation: _animationController,
      builder: (context, child) {
        return Transform.scale(
          scale: _scaleAnimation.value,
          child: Opacity(
            opacity: _fadeAnimation.value,
            child: Container(
              margin: EdgeInsets.only(bottom: 3.h),
              decoration: BoxDecoration(
                color: colorScheme.surface,
                borderRadius: BorderRadius.circular(16),
                border: widget.isBusinessCreator
                    ? Border.all(
                        color: colorScheme.primary.withValues(alpha: 0.3),
                        width: 2,
                      )
                    : null,
                boxShadow: [
                  BoxShadow(
                    color: isDark
                        ? Colors.white.withValues(alpha: 0.05)
                        : Colors.black.withValues(alpha: 0.08),
                    offset: const Offset(0, 4),
                    blurRadius: 12,
                    spreadRadius: 0,
                  ),
                ],
              ),
              child: Padding(
                padding: EdgeInsets.all(4.w),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Header with business creator badge
                    if (widget.isBusinessCreator)
                      Container(
                        margin: EdgeInsets.only(bottom: 2.h),
                        padding: EdgeInsets.symmetric(
                            horizontal: 3.w, vertical: 1.h),
                        decoration: BoxDecoration(
                          gradient: LinearGradient(
                            colors: [
                              colorScheme.primary.withValues(alpha: 0.1),
                              colorScheme.secondary.withValues(alpha: 0.05),
                            ],
                          ),
                          borderRadius: BorderRadius.circular(8),
                          border: Border.all(
                            color: colorScheme.primary.withValues(alpha: 0.2),
                          ),
                        ),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Icon(
                              Icons.star_rounded,
                              color: colorScheme.primary,
                              size: 16,
                            ),
                            SizedBox(width: 1.w),
                            Text(
                              'Business Creator',
                              style: theme.textTheme.bodySmall?.copyWith(
                                color: colorScheme.primary,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                          ],
                        ),
                      ),

                    Row(
                      children: [
                        // Enhanced Avatar with status indicator
                        Stack(
                          children: [
                            Container(
                              decoration: BoxDecoration(
                                shape: BoxShape.circle,
                                border: Border.all(
                                  color: _getRoleColor().withValues(alpha: 0.3),
                                  width: 2,
                                ),
                              ),
                              child: CircleAvatar(
                                radius: 7.w,
                                backgroundColor:
                                    colorScheme.primary.withValues(alpha: 0.1),
                                backgroundImage: widget.member.userAvatarUrl !=
                                        null
                                    ? NetworkImage(widget.member.userAvatarUrl!)
                                    : null,
                                child: widget.member.userAvatarUrl == null
                                    ? Icon(
                                        Icons.person_rounded,
                                        size: 7.w,
                                        color: colorScheme.primary,
                                      )
                                    : null,
                              ),
                            ),
                            // Status indicator
                            Positioned(
                              bottom: 0,
                              right: 0,
                              child: Container(
                                width: 4.w,
                                height: 4.w,
                                decoration: BoxDecoration(
                                  color: widget.member.isActive
                                      ? Colors.green
                                      : Colors.grey,
                                  shape: BoxShape.circle,
                                  border: Border.all(
                                    color: colorScheme.surface,
                                    width: 2,
                                  ),
                                ),
                              ),
                            ),
                          ],
                        ),
                        SizedBox(width: 4.w),

                        // Enhanced Member Info
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Row(
                                children: [
                                  Expanded(
                                    child: Text(
                                      widget.member.userFullName ??
                                          'Unknown User',
                                      style:
                                          theme.textTheme.titleLarge?.copyWith(
                                        fontWeight: FontWeight.bold,
                                        color: colorScheme.onSurface,
                                      ),
                                    ),
                                  ),
                                  _buildEnhancedRoleChip(),
                                ],
                              ),
                              SizedBox(height: 0.8.h),
                              Text(
                                widget.member.userEmail ?? '',
                                style: theme.textTheme.bodyMedium?.copyWith(
                                  color: colorScheme.onSurface
                                      .withValues(alpha: 0.7),
                                ),
                              ),
                              SizedBox(height: 1.h),
                              _buildMemberStats(),
                            ],
                          ),
                        ),

                        // Enhanced Actions Menu
                        if (widget.canManage && !widget.isBusinessCreator)
                          _buildActionsMenu(),
                      ],
                    ),

                    SizedBox(height: 3.h),

                    // Enhanced Member Details
                    _buildDetailSection(),
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  void _handleMenuAction(String action) {
    switch (action) {
      case 'change_role':
        _showRoleChangeDialog();
        break;
      case 'manage_permissions':
        widget.onManagePermissions();
        break;
      case 'remove':
        widget.onRemove();
        break;
    }
  }

  void _showRoleChangeDialog() {
    showDialog(
      context: context,
      builder: (context) => _RoleChangeDialog(
        currentRole: widget.member.role,
        memberName: widget.member.userFullName ?? 'Unknown User',
        isBusinessCreator: widget.isBusinessCreator,
        onRoleChanged: (newRole) {
          Navigator.pop(context);
          widget.onUpdateRole(newRole);
        },
      ),
    );
  }

  Color _getRoleColor() {
    switch (widget.member.role) {
      case MemberRole.partner:
        return Colors.purple;
      case MemberRole.admin:
        return Colors.orange;
      case MemberRole.staff:
        return Colors.blue;
    }
  }

  Widget _buildEnhancedRoleChip() {
    final theme = Theme.of(context);
    final roleColor = _getRoleColor();

    return Container(
      padding: EdgeInsets.symmetric(horizontal: 3.w, vertical: 0.8.h),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            roleColor.withValues(alpha: 0.15),
            roleColor.withValues(alpha: 0.05),
          ],
        ),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: roleColor.withValues(alpha: 0.4),
          width: 1.5,
        ),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            _getRoleIcon(),
            color: roleColor,
            size: 14,
          ),
          SizedBox(width: 1.w),
          Text(
            widget.member.role.displayName.toUpperCase(),
            style: theme.textTheme.bodySmall?.copyWith(
              color: roleColor,
              fontWeight: FontWeight.bold,
              fontSize: 10,
              letterSpacing: 0.5,
            ),
          ),
        ],
      ),
    );
  }

  IconData _getRoleIcon() {
    switch (widget.member.role) {
      case MemberRole.partner:
        return Icons.diamond_rounded;
      case MemberRole.admin:
        return Icons.admin_panel_settings_rounded;
      case MemberRole.staff:
        return Icons.person_rounded;
    }
  }

  Widget _buildMemberStats() {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Row(
      children: [
        _buildStatChip(
          'Joined ${_formatDateShort(widget.member.joinedAt)}',
          Icons.calendar_today_rounded,
          colorScheme.primary.withValues(alpha: 0.7),
        ),
        SizedBox(width: 2.w),
        _buildStatChip(
          widget.member.isActive ? 'Active' : 'Inactive',
          widget.member.isActive
              ? Icons.check_circle_rounded
              : Icons.cancel_rounded,
          widget.member.isActive ? Colors.green : Colors.grey,
        ),
      ],
    );
  }

  Widget _buildStatChip(String text, IconData icon, Color color) {
    final theme = Theme.of(context);

    return Container(
      padding: EdgeInsets.symmetric(horizontal: 2.w, vertical: 0.5.h),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, color: color, size: 12),
          SizedBox(width: 1.w),
          Text(
            text,
            style: theme.textTheme.bodySmall?.copyWith(
              color: color,
              fontWeight: FontWeight.w500,
              fontSize: 10,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildActionsMenu() {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Container(
      decoration: BoxDecoration(
        color: colorScheme.surface,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: colorScheme.outline.withValues(alpha: 0.2),
        ),
      ),
      child: PopupMenuButton<String>(
        onSelected: _handleMenuAction,
        icon: Icon(
          Icons.more_vert_rounded,
          color: colorScheme.onSurface.withValues(alpha: 0.7),
        ),
        itemBuilder: (context) => [
          PopupMenuItem(
            value: 'change_role',
            child: Row(
              children: [
                Icon(Icons.admin_panel_settings_rounded,
                    color: colorScheme.primary),
                SizedBox(width: 2.w),
                Text('Change Role'),
              ],
            ),
          ),
          PopupMenuItem(
            value: 'manage_permissions',
            child: Row(
              children: [
                Icon(Icons.security_rounded, color: colorScheme.secondary),
                SizedBox(width: 2.w),
                Text('Manage Permissions'),
              ],
            ),
          ),
          PopupMenuItem(
            value: 'remove',
            child: Row(
              children: [
                Icon(Icons.remove_circle_rounded, color: Colors.red),
                SizedBox(width: 2.w),
                Text('Remove Member', style: TextStyle(color: Colors.red)),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDetailSection() {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Container(
      padding: EdgeInsets.all(3.w),
      decoration: BoxDecoration(
        color: colorScheme.surface.withValues(alpha: 0.5),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: colorScheme.outline.withValues(alpha: 0.1),
        ),
      ),
      child: Row(
        children: [
          Expanded(
            child: _buildEnhancedDetailItem(
              'Member Since',
              _formatDate(widget.member.joinedAt),
              Icons.calendar_today_rounded,
            ),
          ),
          Container(
            width: 1,
            height: 40,
            color: colorScheme.outline.withValues(alpha: 0.2),
          ),
          Expanded(
            child: _buildEnhancedDetailItem(
              'Status',
              widget.member.isActive ? 'Active' : 'Inactive',
              widget.member.isActive
                  ? Icons.check_circle_rounded
                  : Icons.cancel_rounded,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildEnhancedDetailItem(String label, String value, IconData icon) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Column(
      children: [
        Icon(
          icon,
          color: colorScheme.primary,
          size: 20,
        ),
        SizedBox(height: 0.5.h),
        Text(
          label,
          style: theme.textTheme.bodySmall?.copyWith(
            color: colorScheme.onSurface.withValues(alpha: 0.6),
            fontWeight: FontWeight.w500,
          ),
        ),
        SizedBox(height: 0.3.h),
        Text(
          value,
          style: theme.textTheme.bodyMedium?.copyWith(
            fontWeight: FontWeight.bold,
            color: colorScheme.onSurface,
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  String _formatDateShort(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date);

    if (difference.inDays < 7) {
      return '${difference.inDays}d ago';
    } else if (difference.inDays < 30) {
      final weeks = (difference.inDays / 7).floor();
      return '${weeks}w ago';
    } else if (difference.inDays < 365) {
      final months = (difference.inDays / 30).floor();
      return '${months}m ago';
    } else {
      final years = (difference.inDays / 365).floor();
      return '${years}y ago';
    }
  }

  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date);

    if (difference.inDays == 0) {
      return 'Today';
    } else if (difference.inDays == 1) {
      return 'Yesterday';
    } else if (difference.inDays < 7) {
      return '${difference.inDays} days ago';
    } else if (difference.inDays < 30) {
      final weeks = (difference.inDays / 7).floor();
      return '$weeks week${weeks > 1 ? 's' : ''} ago';
    } else if (difference.inDays < 365) {
      final months = (difference.inDays / 30).floor();
      return '$months month${months > 1 ? 's' : ''} ago';
    } else {
      final years = (difference.inDays / 365).floor();
      return '$years year${years > 1 ? 's' : ''} ago';
    }
  }
}

class _RoleChangeDialog extends StatefulWidget {
  final MemberRole currentRole;
  final String memberName;
  final bool isBusinessCreator;
  final Function(MemberRole) onRoleChanged;

  const _RoleChangeDialog({
    required this.currentRole,
    required this.memberName,
    required this.isBusinessCreator,
    required this.onRoleChanged,
  });

  @override
  State<_RoleChangeDialog> createState() => _RoleChangeDialogState();
}

class _RoleChangeDialogState extends State<_RoleChangeDialog> {
  late MemberRole _selectedRole;

  @override
  void initState() {
    super.initState();
    _selectedRole = widget.currentRole;
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return AlertDialog(
      title: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Change Role',
            style: theme.textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          SizedBox(height: 0.5.h),
          Text(
            widget.memberName,
            style: theme.textTheme.bodyMedium?.copyWith(
              color: colorScheme.onSurface.withValues(alpha: 0.7),
            ),
          ),
        ],
      ),
      content: widget.isBusinessCreator
          ? _buildBusinessCreatorMessage(theme, colorScheme)
          : _buildRoleSelection(theme, colorScheme),
      actions: widget.isBusinessCreator
          ? [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('OK'),
              ),
            ]
          : [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('Cancel'),
              ),
              FilledButton(
                onPressed: _selectedRole != widget.currentRole
                    ? () => widget.onRoleChanged(_selectedRole)
                    : null,
                child: const Text('Update Role'),
              ),
            ],
    );
  }

  Widget _buildBusinessCreatorMessage(
      ThemeData theme, ColorScheme colorScheme) {
    return Container(
      padding: EdgeInsets.all(4.w),
      decoration: BoxDecoration(
        color: colorScheme.primary.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: colorScheme.primary.withValues(alpha: 0.3),
        ),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            Icons.star_rounded,
            color: colorScheme.primary,
            size: 32,
          ),
          SizedBox(height: 2.h),
          Text(
            'Business Creator',
            style: theme.textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
              color: colorScheme.primary,
            ),
          ),
          SizedBox(height: 1.h),
          Text(
            'The business creator\'s role cannot be changed. They have permanent administrative access to all business functions.',
            style: theme.textTheme.bodyMedium?.copyWith(
              color: colorScheme.onSurface.withValues(alpha: 0.8),
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildRoleSelection(ThemeData theme, ColorScheme colorScheme) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Text(
          'Select the new role for this member:',
          style: theme.textTheme.bodyMedium?.copyWith(
            color: colorScheme.onSurface.withValues(alpha: 0.8),
          ),
        ),
        SizedBox(height: 3.h),
        ...MemberRole.values
            .map((role) => _buildRoleOption(role, theme, colorScheme)),
      ],
    );
  }

  Widget _buildRoleOption(
      MemberRole role, ThemeData theme, ColorScheme colorScheme) {
    final isSelected = _selectedRole == role;
    Color roleColor;
    IconData roleIcon;
    String roleDescription;

    switch (role) {
      case MemberRole.partner:
        roleColor = Colors.purple;
        roleIcon = Icons.diamond_rounded;
        roleDescription = 'Full access to all business functions and settings';
        break;
      case MemberRole.admin:
        roleColor = Colors.orange;
        roleIcon = Icons.admin_panel_settings_rounded;
        roleDescription = 'Can manage members and most business operations';
        break;
      case MemberRole.staff:
        roleColor = Colors.blue;
        roleIcon = Icons.person_rounded;
        roleDescription = 'Limited access based on assigned permissions';
        break;
    }

    return GestureDetector(
      onTap: () {
        setState(() {
          _selectedRole = role;
        });
      },
      child: Container(
        margin: EdgeInsets.only(bottom: 2.h),
        padding: EdgeInsets.all(3.w),
        decoration: BoxDecoration(
          color: isSelected
              ? roleColor.withValues(alpha: 0.1)
              : colorScheme.surface,
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: isSelected
                ? roleColor.withValues(alpha: 0.4)
                : colorScheme.outline.withValues(alpha: 0.2),
            width: isSelected ? 2 : 1,
          ),
        ),
        child: Row(
          children: [
            // Selection indicator
            Container(
              width: 20,
              height: 20,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: isSelected
                    ? roleColor
                    : colorScheme.outline.withValues(alpha: 0.3),
                border: Border.all(
                  color: isSelected
                      ? roleColor
                      : colorScheme.outline.withValues(alpha: 0.5),
                  width: 2,
                ),
              ),
              child: isSelected
                  ? Icon(
                      Icons.check_rounded,
                      color: Colors.white,
                      size: 12,
                    )
                  : null,
            ),
            SizedBox(width: 3.w),

            // Role icon
            Container(
              padding: EdgeInsets.all(2.w),
              decoration: BoxDecoration(
                color: roleColor.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(roleIcon, color: roleColor, size: 20),
            ),
            SizedBox(width: 3.w),

            // Role info
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    role.displayName,
                    style: theme.textTheme.titleSmall?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: isSelected ? roleColor : colorScheme.onSurface,
                    ),
                  ),
                  SizedBox(height: 0.3.h),
                  Text(
                    roleDescription,
                    style: theme.textTheme.bodySmall?.copyWith(
                      color: isSelected
                          ? roleColor.withValues(alpha: 0.8)
                          : colorScheme.onSurface.withValues(alpha: 0.6),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
