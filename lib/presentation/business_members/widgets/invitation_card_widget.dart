import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';

import '../../../core/app_export.dart';

class InvitationCardWidget extends StatefulWidget {
  final BusinessInvitationModel invitation;
  final bool canManage;
  final VoidCallback onCancel;
  final VoidCallback onResend;

  const InvitationCardWidget({
    super.key,
    required this.invitation,
    required this.canManage,
    required this.onCancel,
    required this.onResend,
  });

  @override
  State<InvitationCardWidget> createState() => _InvitationCardWidgetState();
}

class _InvitationCardWidgetState extends State<InvitationCardWidget>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  late Animation<double> _fadeAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 400),
      vsync: this,
    );

    _scaleAnimation = Tween<double>(
      begin: 0.95,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOutBack,
    ));

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOut,
    ));

    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;
    final isDark = theme.brightness == Brightness.dark;

    return AnimatedBuilder(
      animation: _animationController,
      builder: (context, child) {
        return Transform.scale(
          scale: _scaleAnimation.value,
          child: Opacity(
            opacity: _fadeAnimation.value,
            child: Container(
              margin: EdgeInsets.only(bottom: 3.h),
              decoration: BoxDecoration(
                color: colorScheme.surface,
                borderRadius: BorderRadius.circular(16),
                border: Border.all(
                  color: _getStatusColor().withValues(alpha: 0.3),
                  width: 2,
                ),
                boxShadow: [
                  BoxShadow(
                    color: isDark
                        ? Colors.white.withValues(alpha: 0.05)
                        : Colors.black.withValues(alpha: 0.08),
                    offset: const Offset(0, 4),
                    blurRadius: 12,
                    spreadRadius: 0,
                  ),
                ],
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Header Section
                  Container(
                    padding: EdgeInsets.all(4.w),
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        begin: Alignment.topLeft,
                        end: Alignment.bottomRight,
                        colors: [
                          _getStatusColor().withValues(alpha: 0.1),
                          _getStatusColor().withValues(alpha: 0.05),
                        ],
                      ),
                      borderRadius: const BorderRadius.only(
                        topLeft: Radius.circular(16),
                        topRight: Radius.circular(16),
                      ),
                    ),
                    child: Row(
                      children: [
                        // Enhanced Status Icon
                        Container(
                          padding: EdgeInsets.all(3.w),
                          decoration: BoxDecoration(
                            color: _getStatusColor().withValues(alpha: 0.2),
                            borderRadius: BorderRadius.circular(12),
                            border: Border.all(
                              color: _getStatusColor().withValues(alpha: 0.4),
                              width: 1.5,
                            ),
                          ),
                          child: Icon(
                            _getStatusIcon(),
                            color: _getStatusColor(),
                            size: 24,
                          ),
                        ),
                        SizedBox(width: 4.w),

                        // Enhanced Invitation Info
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                widget.invitation.email,
                                style: theme.textTheme.titleLarge?.copyWith(
                                  fontWeight: FontWeight.bold,
                                  color: colorScheme.onSurface,
                                ),
                              ),
                              SizedBox(height: 1.h),
                              Row(
                                children: [
                                  _buildEnhancedRoleChip(theme, colorScheme),
                                  SizedBox(width: 2.w),
                                  _buildEnhancedStatusChip(theme, colorScheme),
                                ],
                              ),
                            ],
                          ),
                        ),

                        // Enhanced Actions Menu
                        if (widget.canManage &&
                            widget.invitation.status ==
                                InvitationStatus.pending)
                          _buildActionsMenu(colorScheme),
                      ],
                    ),
                  ),

                  // Content Section
                  Padding(
                    padding: EdgeInsets.all(4.w),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // Invitation Details
                        _buildInvitationDetails(theme, colorScheme),

                        // Inviter Info
                        if (widget.invitation.inviterName != null) ...[
                          SizedBox(height: 2.h),
                          _buildInviterInfo(theme, colorScheme),
                        ],

                        // Expiration Warning
                        if (_isExpiringSoon()) ...[
                          SizedBox(height: 2.h),
                          _buildExpirationWarning(theme, colorScheme),
                        ],
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  Color _getStatusColor() {
    switch (widget.invitation.status) {
      case InvitationStatus.pending:
        return Colors.orange;
      case InvitationStatus.accepted:
        return Colors.green;
      case InvitationStatus.declined:
        return Colors.red;
      case InvitationStatus.expired:
        return Colors.grey;
    }
  }

  IconData _getStatusIcon() {
    switch (widget.invitation.status) {
      case InvitationStatus.pending:
        return Icons.schedule;
      case InvitationStatus.accepted:
        return Icons.check_circle;
      case InvitationStatus.declined:
        return Icons.cancel;
      case InvitationStatus.expired:
        return Icons.access_time;
    }
  }

  void _handleMenuAction(String action) {
    switch (action) {
      case 'resend':
        widget.onResend();
        break;
      case 'cancel':
        widget.onCancel();
        break;
    }
  }

  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date);

    if (difference.inDays == 0) {
      return 'Today';
    } else if (difference.inDays == 1) {
      return 'Yesterday';
    } else if (difference.inDays < 7) {
      return '${difference.inDays} days ago';
    } else if (difference.inDays < 30) {
      final weeks = (difference.inDays / 7).floor();
      return '$weeks week${weeks > 1 ? 's' : ''} ago';
    } else if (difference.inDays < 365) {
      final months = (difference.inDays / 30).floor();
      return '$months month${months > 1 ? 's' : ''} ago';
    } else {
      final years = (difference.inDays / 365).floor();
      return '$years year${years > 1 ? 's' : ''} ago';
    }
  }

  // Enhanced helper methods for modern UI
  Widget _buildEnhancedRoleChip(ThemeData theme, ColorScheme colorScheme) {
    Color chipColor;
    IconData roleIcon;

    switch (widget.invitation.role) {
      case MemberRole.partner:
        chipColor = Colors.purple;
        roleIcon = Icons.diamond_rounded;
        break;
      case MemberRole.admin:
        chipColor = Colors.orange;
        roleIcon = Icons.admin_panel_settings_rounded;
        break;
      case MemberRole.staff:
        chipColor = Colors.blue;
        roleIcon = Icons.person_rounded;
        break;
    }

    return Container(
      padding: EdgeInsets.symmetric(horizontal: 3.w, vertical: 1.h),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            chipColor.withValues(alpha: 0.15),
            chipColor.withValues(alpha: 0.08),
          ],
        ),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: chipColor.withValues(alpha: 0.4), width: 1.5),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(roleIcon, color: chipColor, size: 16),
          SizedBox(width: 1.w),
          Text(
            widget.invitation.role.displayName,
            style: theme.textTheme.bodySmall?.copyWith(
              color: chipColor,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildEnhancedStatusChip(ThemeData theme, ColorScheme colorScheme) {
    final statusColor = _getStatusColor();
    final statusIcon = _getStatusIcon();

    return Container(
      padding: EdgeInsets.symmetric(horizontal: 3.w, vertical: 1.h),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            statusColor.withValues(alpha: 0.15),
            statusColor.withValues(alpha: 0.08),
          ],
        ),
        borderRadius: BorderRadius.circular(16),
        border:
            Border.all(color: statusColor.withValues(alpha: 0.4), width: 1.5),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(statusIcon, color: statusColor, size: 16),
          SizedBox(width: 1.w),
          Text(
            widget.invitation.status.displayName,
            style: theme.textTheme.bodySmall?.copyWith(
              color: statusColor,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildActionsMenu(ColorScheme colorScheme) {
    return Container(
      decoration: BoxDecoration(
        color: colorScheme.surface,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: colorScheme.outline.withValues(alpha: 0.2),
        ),
      ),
      child: PopupMenuButton<String>(
        onSelected: _handleMenuAction,
        icon: Icon(
          Icons.more_vert_rounded,
          color: colorScheme.onSurface.withValues(alpha: 0.7),
        ),
        itemBuilder: (context) => [
          PopupMenuItem(
            value: 'resend',
            child: Row(
              children: [
                Icon(Icons.refresh_rounded, color: Colors.blue),
                SizedBox(width: 2.w),
                Text('Resend Invitation'),
              ],
            ),
          ),
          PopupMenuItem(
            value: 'cancel',
            child: Row(
              children: [
                Icon(Icons.cancel_rounded, color: Colors.red),
                SizedBox(width: 2.w),
                Text('Cancel Invitation', style: TextStyle(color: Colors.red)),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildInvitationDetails(ThemeData theme, ColorScheme colorScheme) {
    return Container(
      padding: EdgeInsets.all(3.w),
      decoration: BoxDecoration(
        color: colorScheme.surface,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: colorScheme.outline.withValues(alpha: 0.1),
        ),
      ),
      child: Row(
        children: [
          Expanded(
            child: _buildEnhancedDetailItem(
              'Sent',
              _formatDate(widget.invitation.createdAt),
              Icons.send_rounded,
              Colors.blue,
              theme,
              colorScheme,
            ),
          ),
          Container(
            width: 1,
            height: 40,
            color: colorScheme.outline.withValues(alpha: 0.2),
          ),
          Expanded(
            child: _buildEnhancedDetailItem(
              'Expires',
              _formatDate(widget.invitation.expiresAt),
              Icons.schedule_rounded,
              _isExpiringSoon() ? Colors.orange : Colors.grey,
              theme,
              colorScheme,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildEnhancedDetailItem(
    String label,
    String value,
    IconData icon,
    Color iconColor,
    ThemeData theme,
    ColorScheme colorScheme,
  ) {
    return Column(
      children: [
        Icon(icon, color: iconColor, size: 24),
        SizedBox(height: 1.h),
        Text(
          label,
          style: theme.textTheme.bodySmall?.copyWith(
            color: colorScheme.onSurface.withValues(alpha: 0.6),
            fontWeight: FontWeight.w500,
          ),
        ),
        SizedBox(height: 0.5.h),
        Text(
          value,
          style: theme.textTheme.bodyMedium?.copyWith(
            fontWeight: FontWeight.bold,
            color: colorScheme.onSurface,
          ),
        ),
      ],
    );
  }

  Widget _buildInviterInfo(ThemeData theme, ColorScheme colorScheme) {
    return Container(
      padding: EdgeInsets.all(3.w),
      decoration: BoxDecoration(
        color: colorScheme.primary.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: colorScheme.primary.withValues(alpha: 0.2),
        ),
      ),
      child: Row(
        children: [
          Container(
            padding: EdgeInsets.all(2.w),
            decoration: BoxDecoration(
              color: colorScheme.primary.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(
              Icons.person_outline_rounded,
              color: colorScheme.primary,
              size: 20,
            ),
          ),
          SizedBox(width: 3.w),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Invited by',
                  style: theme.textTheme.bodySmall?.copyWith(
                    color: colorScheme.onSurface.withValues(alpha: 0.6),
                  ),
                ),
                Text(
                  widget.invitation.inviterName!,
                  style: theme.textTheme.bodyMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: colorScheme.primary,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildExpirationWarning(ThemeData theme, ColorScheme colorScheme) {
    return Container(
      padding: EdgeInsets.all(3.w),
      decoration: BoxDecoration(
        color: Colors.orange.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Colors.orange.withValues(alpha: 0.3),
        ),
      ),
      child: Row(
        children: [
          Icon(
            Icons.warning_rounded,
            color: Colors.orange,
            size: 20,
          ),
          SizedBox(width: 2.w),
          Expanded(
            child: Text(
              'This invitation expires soon!',
              style: theme.textTheme.bodySmall?.copyWith(
                color: Colors.orange.shade700,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ],
      ),
    );
  }

  bool _isExpiringSoon() {
    final now = DateTime.now();
    final expiresAt = widget.invitation.expiresAt;
    final difference = expiresAt.difference(now);
    return difference.inDays <= 2 && difference.inDays >= 0;
  }
}
