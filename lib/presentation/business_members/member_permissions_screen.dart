import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';

import '../../core/app_export.dart';

class MemberPermissionsScreen extends StatefulWidget {
  final BusinessMemberModel member;
  final String businessId;

  const MemberPermissionsScreen({
    super.key,
    required this.member,
    required this.businessId,
  });

  @override
  State<MemberPermissionsScreen> createState() =>
      _MemberPermissionsScreenState();
}

class _MemberPermissionsScreenState extends State<MemberPermissionsScreen>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;

  // Data
  List<CashbookModel> _cashbooks = [];
  List<MemberCashbookPermissionModel> _permissions = [];
  bool _isLoading = true;
  String? _error;
  bool _hasChanges = false;

  // Services
  late BusinessMemberService _memberService;
  late DataService _dataService;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOut,
    ));

    _memberService = BusinessMemberService.instance;
    _dataService = DataService.instance;
    _loadData();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  Future<void> _loadData() async {
    try {
      setState(() {
        _isLoading = true;
        _error = null;
      });

      final results = await Future.wait([
        _dataService.getCashbooksByBusiness(widget.businessId),
        _memberService.getMemberCashbookPermissions(widget.member.id),
      ]);

      _cashbooks = results[0] as List<CashbookModel>;
      _permissions = results[1] as List<MemberCashbookPermissionModel>;

      _animationController.forward();
    } catch (e) {
      setState(() {
        _error = e.toString();
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _updatePermission(
      String cashbookId, CashbookPermission newPermission) async {
    try {
      // Find existing permission
      final existingPermission =
          _permissions.where((p) => p.cashbookId == cashbookId).firstOrNull;

      if (existingPermission != null) {
        // Update existing permission
        await _memberService.updateCashbookPermission(
          existingPermission.id,
          newPermission,
          widget.businessId,
        );
      } else {
        // Grant new permission
        await _memberService.grantCashbookPermission(
          businessMemberId: widget.member.id,
          cashbookId: cashbookId,
          permission: newPermission,
          businessId: widget.businessId,
        );
      }

      // Reload data to reflect changes
      await _loadData();

      setState(() {
        _hasChanges = true;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Permission updated successfully'),
            backgroundColor: Colors.green,
            behavior: SnackBarBehavior.floating,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to update permission: ${e.toString()}'),
            backgroundColor: Colors.red,
            behavior: SnackBarBehavior.floating,
          ),
        );
      }
    }
  }

  Future<void> _revokePermission(String permissionId) async {
    try {
      await _memberService.revokeCashbookPermission(
          permissionId, widget.businessId);
      await _loadData();

      setState(() {
        _hasChanges = true;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Permission revoked successfully'),
            backgroundColor: Colors.orange,
            behavior: SnackBarBehavior.floating,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to revoke permission: ${e.toString()}'),
            backgroundColor: Colors.red,
            behavior: SnackBarBehavior.floating,
          ),
        );
      }
    }
  }

  CashbookPermission? _getPermissionForCashbook(String cashbookId) {
    final permission =
        _permissions.where((p) => p.cashbookId == cashbookId).firstOrNull;
    return permission?.permission;
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;
    final isDark = theme.brightness == Brightness.dark;

    return PopScope(
      canPop: !_hasChanges,
      onPopInvokedWithResult: (didPop, result) {
        if (!didPop && _hasChanges) {
          _showUnsavedChangesDialog();
        }
      },
      child: Scaffold(
        backgroundColor: colorScheme.surface,
        appBar: _buildAppBar(theme, colorScheme),
        body: _isLoading
            ? _buildLoadingState(colorScheme)
            : _error != null
                ? _buildErrorState(theme, colorScheme)
                : _buildContent(theme, colorScheme, isDark),
      ),
    );
  }

  PreferredSizeWidget _buildAppBar(ThemeData theme, ColorScheme colorScheme) {
    return AppBar(
      elevation: 0,
      backgroundColor: Colors.transparent,
      surfaceTintColor: Colors.transparent,
      leading: IconButton(
        onPressed: () {
          if (_hasChanges) {
            _showUnsavedChangesDialog();
          } else {
            Navigator.pop(context);
          }
        },
        icon: Icon(
          Icons.arrow_back_rounded,
          color: colorScheme.onSurface,
        ),
      ),
      title: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Manage Permissions',
            style: theme.textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.bold,
              color: colorScheme.onSurface,
            ),
          ),
          Text(
            widget.member.userFullName ?? 'Unknown User',
            style: theme.textTheme.bodyMedium?.copyWith(
              color: colorScheme.onSurface.withValues(alpha: 0.7),
            ),
          ),
        ],
      ),
      actions: [
        if (_hasChanges)
          Container(
            margin: EdgeInsets.only(right: 4.w),
            padding: EdgeInsets.symmetric(horizontal: 3.w, vertical: 1.h),
            decoration: BoxDecoration(
              color: colorScheme.primary.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(20),
              border: Border.all(
                color: colorScheme.primary.withValues(alpha: 0.3),
              ),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(
                  Icons.edit_rounded,
                  color: colorScheme.primary,
                  size: 16,
                ),
                SizedBox(width: 1.w),
                Text(
                  'Modified',
                  style: theme.textTheme.bodySmall?.copyWith(
                    color: colorScheme.primary,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
          ),
      ],
    );
  }

  Widget _buildLoadingState(ColorScheme colorScheme) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            padding: EdgeInsets.all(4.w),
            decoration: BoxDecoration(
              color: colorScheme.primary.withValues(alpha: 0.1),
              shape: BoxShape.circle,
            ),
            child: CircularProgressIndicator(
              color: colorScheme.primary,
              strokeWidth: 3,
            ),
          ),
          SizedBox(height: 3.h),
          Text(
            'Loading permissions...',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  color: colorScheme.onSurface.withValues(alpha: 0.7),
                ),
          ),
        ],
      ),
    );
  }

  Widget _buildContent(ThemeData theme, ColorScheme colorScheme, bool isDark) {
    return AnimatedBuilder(
      animation: _fadeAnimation,
      builder: (context, child) {
        return Opacity(
          opacity: _fadeAnimation.value,
          child: Column(
            children: [
              // Member Info Header
              _buildMemberHeader(theme, colorScheme),

              // Permissions List
              Expanded(
                child: _cashbooks.isEmpty
                    ? _buildEmptyState(theme, colorScheme)
                    : _buildPermissionsList(theme, colorScheme, isDark),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildMemberHeader(ThemeData theme, ColorScheme colorScheme) {
    return Container(
      margin: EdgeInsets.all(4.w),
      padding: EdgeInsets.all(4.w),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            colorScheme.primary.withValues(alpha: 0.1),
            colorScheme.secondary.withValues(alpha: 0.05),
          ],
        ),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: colorScheme.outline.withValues(alpha: 0.2),
        ),
      ),
      child: Row(
        children: [
          // Avatar
          Container(
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              border: Border.all(
                color: colorScheme.primary.withValues(alpha: 0.3),
                width: 2,
              ),
            ),
            child: CircleAvatar(
              radius: 6.w,
              backgroundColor: colorScheme.primary.withValues(alpha: 0.1),
              backgroundImage: widget.member.userAvatarUrl != null
                  ? NetworkImage(widget.member.userAvatarUrl!)
                  : null,
              child: widget.member.userAvatarUrl == null
                  ? Icon(
                      Icons.person_rounded,
                      size: 6.w,
                      color: colorScheme.primary,
                    )
                  : null,
            ),
          ),
          SizedBox(width: 4.w),

          // Member Info
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  widget.member.userFullName ?? 'Unknown User',
                  style: theme.textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: colorScheme.onSurface,
                  ),
                ),
                SizedBox(height: 0.5.h),
                Text(
                  widget.member.userEmail ?? '',
                  style: theme.textTheme.bodyMedium?.copyWith(
                    color: colorScheme.onSurface.withValues(alpha: 0.7),
                  ),
                ),
                SizedBox(height: 1.h),
                _buildRoleChip(theme, colorScheme),
              ],
            ),
          ),

          // Permission Summary
          _buildPermissionSummary(theme, colorScheme),
        ],
      ),
    );
  }

  Widget _buildRoleChip(ThemeData theme, ColorScheme colorScheme) {
    Color roleColor;
    IconData roleIcon;

    switch (widget.member.role) {
      case MemberRole.partner:
        roleColor = Colors.purple;
        roleIcon = Icons.diamond_rounded;
        break;
      case MemberRole.admin:
        roleColor = Colors.orange;
        roleIcon = Icons.admin_panel_settings_rounded;
        break;
      case MemberRole.staff:
        roleColor = Colors.blue;
        roleIcon = Icons.person_rounded;
        break;
    }

    return Container(
      padding: EdgeInsets.symmetric(horizontal: 3.w, vertical: 0.8.h),
      decoration: BoxDecoration(
        color: roleColor.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: roleColor.withValues(alpha: 0.3),
        ),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(roleIcon, color: roleColor, size: 14),
          SizedBox(width: 1.w),
          Text(
            widget.member.role.displayName.toUpperCase(),
            style: theme.textTheme.bodySmall?.copyWith(
              color: roleColor,
              fontWeight: FontWeight.bold,
              fontSize: 10,
              letterSpacing: 0.5,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPermissionSummary(ThemeData theme, ColorScheme colorScheme) {
    final totalCashbooks = _cashbooks.length;
    final permissionsCount = _permissions.length;
    final writePermissions = _permissions
        .where((p) => p.permission == CashbookPermission.write)
        .length;

    return Column(
      children: [
        _buildSummaryItem(
          '$permissionsCount/$totalCashbooks',
          'Access',
          Icons.security_rounded,
          colorScheme.primary,
          theme,
        ),
        SizedBox(height: 1.h),
        _buildSummaryItem(
          '$writePermissions',
          'Write',
          Icons.edit_rounded,
          Colors.green,
          theme,
        ),
      ],
    );
  }

  Widget _buildSummaryItem(
    String value,
    String label,
    IconData icon,
    Color color,
    ThemeData theme,
  ) {
    return Column(
      children: [
        Icon(icon, color: color, size: 20),
        SizedBox(height: 0.3.h),
        Text(
          value,
          style: theme.textTheme.titleSmall?.copyWith(
            fontWeight: FontWeight.bold,
            color: color,
          ),
        ),
        Text(
          label,
          style: theme.textTheme.bodySmall?.copyWith(
            color: color.withValues(alpha: 0.7),
            fontSize: 10,
          ),
        ),
      ],
    );
  }

  Widget _buildEmptyState(ThemeData theme, ColorScheme colorScheme) {
    return Center(
      child: Padding(
        padding: EdgeInsets.all(6.w),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              padding: EdgeInsets.all(4.w),
              decoration: BoxDecoration(
                color: colorScheme.primary.withValues(alpha: 0.1),
                shape: BoxShape.circle,
              ),
              child: Icon(
                Icons.folder_open_rounded,
                size: 48,
                color: colorScheme.primary,
              ),
            ),
            SizedBox(height: 3.h),
            Text(
              'No Cashbooks Found',
              style: theme.textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            SizedBox(height: 1.h),
            Text(
              'This business doesn\'t have any cashbooks yet.',
              style: theme.textTheme.bodyMedium?.copyWith(
                color: colorScheme.onSurface.withValues(alpha: 0.7),
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildErrorState(ThemeData theme, ColorScheme colorScheme) {
    return Center(
      child: Padding(
        padding: EdgeInsets.all(6.w),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              padding: EdgeInsets.all(4.w),
              decoration: BoxDecoration(
                color: colorScheme.error.withValues(alpha: 0.1),
                shape: BoxShape.circle,
              ),
              child: Icon(
                Icons.error_outline_rounded,
                size: 48,
                color: colorScheme.error,
              ),
            ),
            SizedBox(height: 3.h),
            Text(
              'Failed to load permissions',
              style: theme.textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            SizedBox(height: 1.h),
            Text(
              _error!,
              style: theme.textTheme.bodyMedium?.copyWith(
                color: colorScheme.onSurface.withValues(alpha: 0.7),
              ),
              textAlign: TextAlign.center,
            ),
            SizedBox(height: 4.h),
            FilledButton.icon(
              onPressed: _loadData,
              icon: const Icon(Icons.refresh_rounded),
              label: const Text('Try Again'),
              style: FilledButton.styleFrom(
                padding: EdgeInsets.symmetric(horizontal: 6.w, vertical: 1.5.h),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPermissionsList(
      ThemeData theme, ColorScheme colorScheme, bool isDark) {
    return ListView.builder(
      padding: EdgeInsets.symmetric(horizontal: 4.w),
      itemCount: _cashbooks.length,
      itemBuilder: (context, index) {
        final cashbook = _cashbooks[index];
        final currentPermission = _getPermissionForCashbook(cashbook.id);

        return _buildCashbookPermissionCard(
          cashbook,
          currentPermission,
          theme,
          colorScheme,
          isDark,
        );
      },
    );
  }

  Widget _buildCashbookPermissionCard(
    CashbookModel cashbook,
    CashbookPermission? currentPermission,
    ThemeData theme,
    ColorScheme colorScheme,
    bool isDark,
  ) {
    return Container(
      margin: EdgeInsets.only(bottom: 3.h),
      decoration: BoxDecoration(
        color: colorScheme.surface,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: colorScheme.outline.withValues(alpha: 0.2),
        ),
        boxShadow: [
          BoxShadow(
            color: isDark
                ? Colors.white.withValues(alpha: 0.05)
                : Colors.black.withValues(alpha: 0.08),
            offset: const Offset(0, 2),
            blurRadius: 8,
            spreadRadius: 0,
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Cashbook Header
          Container(
            padding: EdgeInsets.all(4.w),
            decoration: BoxDecoration(
              color: colorScheme.primary.withValues(alpha: 0.05),
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(16),
                topRight: Radius.circular(16),
              ),
            ),
            child: Row(
              children: [
                // Cashbook Icon
                Container(
                  padding: EdgeInsets.all(2.w),
                  decoration: BoxDecoration(
                    color: colorScheme.primary.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(
                    Icons.account_balance_wallet_rounded,
                    color: colorScheme.primary,
                    size: 20,
                  ),
                ),
                SizedBox(width: 3.w),

                // Cashbook Info
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        cashbook.name,
                        style: theme.textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                          color: colorScheme.onSurface,
                        ),
                      ),
                      if (cashbook.description != null) ...[
                        SizedBox(height: 0.3.h),
                        Text(
                          cashbook.description!,
                          style: theme.textTheme.bodySmall?.copyWith(
                            color: colorScheme.onSurface.withValues(alpha: 0.6),
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ],
                    ],
                  ),
                ),

                // Current Permission Badge
                if (currentPermission != null)
                  _buildPermissionBadge(currentPermission, theme, colorScheme),
              ],
            ),
          ),

          // Permission Options
          Padding(
            padding: EdgeInsets.all(4.w),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Access Level',
                  style: theme.textTheme.titleSmall?.copyWith(
                    fontWeight: FontWeight.w600,
                    color: colorScheme.onSurface,
                  ),
                ),
                SizedBox(height: 2.h),

                // Permission Options
                _buildPermissionOption(
                  cashbook.id,
                  null,
                  'No Access',
                  'Member cannot view this cashbook',
                  Icons.block_rounded,
                  Colors.grey,
                  currentPermission,
                  theme,
                  colorScheme,
                ),
                SizedBox(height: 1.5.h),
                _buildPermissionOption(
                  cashbook.id,
                  CashbookPermission.read,
                  'Read Only',
                  'Can view transactions and reports',
                  Icons.visibility_rounded,
                  Colors.blue,
                  currentPermission,
                  theme,
                  colorScheme,
                ),
                SizedBox(height: 1.5.h),
                _buildPermissionOption(
                  cashbook.id,
                  CashbookPermission.write,
                  'Read & Write',
                  'Can add, edit, and delete transactions',
                  Icons.edit_rounded,
                  Colors.green,
                  currentPermission,
                  theme,
                  colorScheme,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPermissionBadge(
    CashbookPermission permission,
    ThemeData theme,
    ColorScheme colorScheme,
  ) {
    Color badgeColor;
    IconData badgeIcon;

    switch (permission) {
      case CashbookPermission.read:
        badgeColor = Colors.blue;
        badgeIcon = Icons.visibility_rounded;
        break;
      case CashbookPermission.write:
        badgeColor = Colors.green;
        badgeIcon = Icons.edit_rounded;
        break;
    }

    return Container(
      padding: EdgeInsets.symmetric(horizontal: 2.w, vertical: 0.5.h),
      decoration: BoxDecoration(
        color: badgeColor.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: badgeColor.withValues(alpha: 0.3),
        ),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(badgeIcon, color: badgeColor, size: 12),
          SizedBox(width: 1.w),
          Text(
            permission.displayName,
            style: theme.textTheme.bodySmall?.copyWith(
              color: badgeColor,
              fontWeight: FontWeight.w600,
              fontSize: 10,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPermissionOption(
    String cashbookId,
    CashbookPermission? permission,
    String title,
    String description,
    IconData icon,
    Color color,
    CashbookPermission? currentPermission,
    ThemeData theme,
    ColorScheme colorScheme,
  ) {
    final isSelected = currentPermission == permission;
    final isNoAccess = permission == null && currentPermission == null;
    final isCurrentlySelected = isSelected || isNoAccess;

    return GestureDetector(
      onTap: () {
        if (permission != null) {
          _updatePermission(cashbookId, permission);
        } else {
          // Revoke permission
          final existingPermission =
              _permissions.where((p) => p.cashbookId == cashbookId).firstOrNull;
          if (existingPermission != null) {
            _revokePermission(existingPermission.id);
          }
        }
      },
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 200),
        padding: EdgeInsets.all(3.w),
        decoration: BoxDecoration(
          color: isCurrentlySelected
              ? color.withValues(alpha: 0.1)
              : colorScheme.surface,
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: isCurrentlySelected
                ? color.withValues(alpha: 0.4)
                : colorScheme.outline.withValues(alpha: 0.2),
            width: isCurrentlySelected ? 2 : 1,
          ),
        ),
        child: Row(
          children: [
            // Selection Indicator
            Container(
              width: 20,
              height: 20,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: isCurrentlySelected
                    ? color
                    : colorScheme.outline.withValues(alpha: 0.3),
                border: Border.all(
                  color: isCurrentlySelected
                      ? color
                      : colorScheme.outline.withValues(alpha: 0.5),
                  width: 2,
                ),
              ),
              child: isCurrentlySelected
                  ? Icon(
                      Icons.check_rounded,
                      color: Colors.white,
                      size: 12,
                    )
                  : null,
            ),
            SizedBox(width: 3.w),

            // Icon
            Container(
              padding: EdgeInsets.all(1.5.w),
              decoration: BoxDecoration(
                color: color.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(icon, color: color, size: 16),
            ),
            SizedBox(width: 3.w),

            // Content
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: theme.textTheme.titleSmall?.copyWith(
                      fontWeight: FontWeight.w600,
                      color:
                          isCurrentlySelected ? color : colorScheme.onSurface,
                    ),
                  ),
                  SizedBox(height: 0.3.h),
                  Text(
                    description,
                    style: theme.textTheme.bodySmall?.copyWith(
                      color: isCurrentlySelected
                          ? color.withValues(alpha: 0.8)
                          : colorScheme.onSurface.withValues(alpha: 0.6),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _showUnsavedChangesDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Unsaved Changes'),
        content: const Text(
          'You have unsaved changes. Are you sure you want to leave without saving?',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context); // Close dialog
              Navigator.pop(context); // Close screen
            },
            child: const Text('Leave'),
          ),
        ],
      ),
    );
  }
}
