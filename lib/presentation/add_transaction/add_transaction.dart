import 'package:camera/camera.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import 'package:sizer/sizer.dart';
import 'package:google_fonts/google_fonts.dart';

import '../../core/app_export.dart';
import '../../theme/app_theme.dart';
import '../../widgets/custom_icon_widget.dart';
import './widgets/amount_input_field.dart';
import './widgets/category_selector.dart';
import './widgets/date_picker_field.dart';
import './widgets/description_input_field.dart';
import './widgets/payment_mode_selector.dart';
import './widgets/receipt_attachment_widget.dart';
import './widgets/transaction_type_selector.dart';

class AddTransaction extends StatefulWidget {
  const AddTransaction({super.key});

  @override
  State<AddTransaction> createState() => _AddTransactionState();
}

class _AddTransactionState extends State<AddTransaction> {
  final _formKey = GlobalKey<FormState>();
  final _amountController = TextEditingController();
  final _descriptionController = TextEditingController();

  bool _isIncome = true;
  DateTime _selectedDate = DateTime.now();
  CategoryModel? _selectedCategory;
  PaymentModeModel? _selectedPaymentMode;
  List<XFile> _attachedImages = [];
  String? _cashbookId;
  String? _transactionId; // For editing existing transaction
  bool _isEditMode = false;
  String? _currency = 'MWK';

  List<CategoryModel> _categories = [];
  List<PaymentModeModel> _paymentModes = [];

  // Form validation states
  String? _amountError;
  String? _descriptionError;
  bool _isFormValid = false;

  // Services
  late DataService _dataService;
  late AuthService _authService;

  @override
  void initState() {
    super.initState();
    _dataService = DataService.instance;
    _authService = AuthService.instance;
    _amountController.addListener(_validateForm);
    _descriptionController.addListener(_validateForm);
    _loadCategories();
    _setCurrency();
    _loadPaymentModes();
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    _handleNavigationArguments();
  }

  void _setCurrency() async {
    await this._dataService.getCashbookById(_cashbookId!).then((cashbook) {
      setState(() {
        this._currency = cashbook?.currency;
      });
    });
  }

  void _handleNavigationArguments() {
    final args =
        ModalRoute.of(context)?.settings.arguments as Map<String, dynamic>;

    if (args.isNotEmpty) {
      _cashbookId = args['cashbookId'] as String?;
      _transactionId = args['transactionId'] as String?;
      _isEditMode = args['mode'] == 'edit' || args['isEdit'] == true;

      if (args['type'] != null) {
        setState(() {
          _isIncome = args['type'] == 'income';
        });
      }

      // If editing, load transaction data
      if (_isEditMode && _transactionId != null) {
        _loadTransactionForEdit();
      }
    }
  }

  Future<void> _loadCategories() async {
    List<CategoryModel> categories = await _dataService.getAllCategories();
    setState(() {
      _categories = categories;
    });
  }

  Future<void> _loadPaymentModes() async {
    List<PaymentModeModel> modes = await _dataService.getAllPaymentModes();
    setState(() {
      _paymentModes = modes;
    });
  }

  Future<void> _loadTransactionForEdit() async {
    try {
      final transaction =
          await _dataService.getTransactionById(_transactionId!);
      if (transaction != null) {
        setState(() {
          _amountController.text = transaction.amount.toString();
          _descriptionController.text = transaction.description;
          _isIncome = transaction.type == TransactionType.income;
          _selectedDate = transaction.transactionDate;
          _selectedCategory =
              _categories.firstWhere((c) => c.id == transaction.categoryId);
          // _selectedPaymentMode = transaction.paymentModeId;
          _cashbookId = transaction.cashbookId;
        });
        _validateForm();
      }
    } catch (error) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Failed to load transaction: $error')),
      );
    }
  }

  @override
  void dispose() {
    _amountController.dispose();
    _descriptionController.dispose();
    super.dispose();
  }

  void _validateForm() {
    setState(() {
      _amountError = null;
      _descriptionError = null;
      _isIncome = _isIncome;

      // Validate amount
      if (_amountController.text.isEmpty) {
        _amountError = 'Amount is required';
      } else {
        final amount = double.tryParse(_amountController.text);
        if (amount == null || amount <= 0) {
          _amountError = 'Please enter a valid amount';
        }
      }

      // Validate description
      if (_descriptionController.text.trim().isEmpty) {
        _descriptionError = 'Description is required';
      }

      // Check overall form validity
      _isFormValid = _amountError == null &&
          _descriptionError == null &&
          _cashbookId != null;
    });
  }

  void _onTransactionTypeChanged(bool isIncome) {
    setState(() {
      _isIncome = isIncome;
      _selectedCategory = null; // Reset category when type changes
    });
    _validateForm();
  }

  void _onCategorySelected(CategoryModel category) {
    setState(() {
      _selectedCategory = category;
    });
    _validateForm();
  }

  void _onPaymentModeSelected(PaymentModeModel paymentMode) {
    setState(() {
      _selectedPaymentMode = paymentMode;
    });
    _validateForm();
  }

  void _onImagesChanged(List<XFile> images) {
    setState(() {
      _attachedImages = images;
    });
  }

  void _onDateChanged(DateTime date) {
    setState(() {
      _selectedDate = date;
    });
  }

  Future<void> _saveTransaction() async {
    if (!_isFormValid || _cashbookId == null) return;

    if (!_authService.isAuthenticated) {
      Navigator.pushReplacementNamed(context, '/login');
      return;
    }

    // Provide haptic feedback
    HapticFeedback.lightImpact();

    // Show loading indicator
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => const Center(
        child: CircularProgressIndicator(),
      ),
    );

    try {
      final amount = double.parse(_amountController.text);
      final description = _descriptionController.text.trim();
      final transactionType =
          _isIncome ? TransactionType.income : TransactionType.expense;

      if (_isEditMode && _transactionId != null) {
        // Update existing transaction
        await _dataService.updateTransaction(
          transactionId: _transactionId!,
          type: transactionType,
          amount: amount,
          description: description,
          transactionDate: _selectedDate,
          categoryId: _selectedCategory!.id,
          paymentModeId: _selectedPaymentMode!.id,
        );
      } else {
        // Create new transaction
        await _dataService.createTransaction(
          cashbookId: _cashbookId!,
          type: transactionType,
          amount: amount,
          description: description,
          transactionDate: _selectedDate,
          categoryId: _selectedCategory!.id,
          paymentModeId: _selectedPaymentMode!.id,
        );
      }

      // Close loading dialog
      if (mounted) {
        Navigator.pop(context);
      }

      // Show success message
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              _isEditMode
                  ? 'Transaction updated successfully!'
                  : 'Transaction saved successfully!',
              style: GoogleFonts.inter(
                fontSize: 14.sp,
                fontWeight: FontWeight.w500,
                color: Colors.white,
              ),
            ),
            backgroundColor: AppTheme.getSuccessColor(
                Theme.of(context).brightness == Brightness.dark),
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8.0),
            ),
            duration: const Duration(seconds: 2),
          ),
        );

        // Navigate back to dashboard
        Navigator.pushNamedAndRemoveUntil(
          context,
          '/dashboard',
          (route) => false,
        );
      }
    } catch (error) {
      // Close loading dialog
      if (mounted) {
        Navigator.pop(context);
      }

      // Show error message
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'Failed to save transaction: $error',
              style: GoogleFonts.inter(
                fontSize: 14.sp,
                fontWeight: FontWeight.w500,
                color: Colors.white,
              ),
            ),
            backgroundColor: AppTheme.getErrorColor(
                Theme.of(context).brightness == Brightness.dark),
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8.0),
            ),
          ),
        );
      }
    }
  }

  Future<bool> _onWillPop() async {
    // Check if form has unsaved changes
    final hasChanges = _amountController.text.isNotEmpty ||
        _descriptionController.text.isNotEmpty ||
        _selectedCategory != null ||
        _selectedPaymentMode != null ||
        _attachedImages.isNotEmpty;

    if (!hasChanges) return true;

    // Show unsaved changes dialog
    final shouldPop = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: Theme.of(context).colorScheme.surface,
        title: Text(
          'Unsaved Changes',
          style: GoogleFonts.inter(
            fontSize: 18.sp,
            fontWeight: FontWeight.w600,
            color: Theme.of(context).brightness == Brightness.dark
                ? Colors.white
                : const Color(0xFF212121),
          ),
        ),
        content: Text(
          'You have unsaved changes. Are you sure you want to leave?',
          style: GoogleFonts.inter(
            fontSize: 14.sp,
            fontWeight: FontWeight.w400,
            color: Theme.of(context).brightness == Brightness.dark
                ? const Color(0xFFB0B0B0)
                : const Color(0xFF757575),
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: Text(
              'Stay',
              style: GoogleFonts.inter(
                fontSize: 14.sp,
                fontWeight: FontWeight.w500,
                color: Theme.of(context).brightness == Brightness.dark
                    ? const Color(0xFF4A90E2)
                    : const Color(0xFF1B365D),
              ),
            ),
          ),
          TextButton(
            onPressed: () => Navigator.pop(context, true),
            child: Text(
              'Leave',
              style: GoogleFonts.inter(
                fontSize: 14.sp,
                fontWeight: FontWeight.w500,
                color: AppTheme.getErrorColor(
                    Theme.of(context).brightness == Brightness.dark),
              ),
            ),
          ),
        ],
      ),
    );

    return shouldPop ?? false;
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    // Show error if no cashbook ID and not in edit mode
    if (_cashbookId == null) {
      return Scaffold(
        backgroundColor: theme.scaffoldBackgroundColor,
        appBar: AppBar(
          backgroundColor: theme.colorScheme.surface,
          title: const Text('Add Transaction'),
        ),
        body: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.error_outline,
                size: 64,
                color: theme.colorScheme.error,
              ),
              SizedBox(height: 2.h),
              Text(
                'No Cashbook Selected',
                style: theme.textTheme.headlineSmall,
              ),
              SizedBox(height: 1.h),
              Text(
                'Please select a cashbook first',
                style: theme.textTheme.bodyMedium,
              ),
              SizedBox(height: 3.h),
              ElevatedButton(
                onPressed: () =>
                    Navigator.pushReplacementNamed(context, '/dashboard'),
                child: const Text('Go to Dashboard'),
              ),
            ],
          ),
        ),
      );
    }

    return WillPopScope(
      onWillPop: _onWillPop,
      child: Scaffold(
        backgroundColor: theme.scaffoldBackgroundColor,
        appBar: AppBar(
          backgroundColor: theme.colorScheme.surface,
          elevation: 1.0,
          shadowColor: isDark
              ? Colors.white.withValues(alpha: 0.1)
              : Colors.black.withValues(alpha: 0.04),
          leading: IconButton(
            icon: CustomIconWidget(
              iconName: 'arrow_back',
              color: isDark ? Colors.white : const Color(0xFF212121),
              size: 24,
            ),
            onPressed: () async {
              if (await _onWillPop()) {
                if (mounted) {
                  Navigator.pop(context);
                }
              }
            },
          ),
          title: Text(
            _isEditMode
                ? (_isIncome ? 'Edit Income' : 'Edit Expense')
                : (_isIncome ? 'Add Income' : 'Add Expense'),
            style: GoogleFonts.inter(
              fontSize: 20.sp,
              fontWeight: FontWeight.w600,
              color: isDark ? Colors.white : const Color(0xFF212121),
              letterSpacing: 0.15,
            ),
          ),
          centerTitle: false,
        ),
        body: SafeArea(
          child: Column(
            children: [
              Expanded(
                child: SingleChildScrollView(
                  padding: EdgeInsets.all(4.w),
                  child: Form(
                    key: _formKey,
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // Transaction Type Selector
                        TransactionTypeSelector(
                          isIncome: _isIncome,
                          onTypeChanged: _onTransactionTypeChanged,
                        ),
                        SizedBox(height: 3.h),

                        // Amount Input Field
                        AmountInputField(
                          controller: _amountController,
                          isIncome: _isIncome,
                          errorText: _amountError,
                          onChanged: (value) => _validateForm(),
                          currency: this._currency ?? '\$',
                        ),
                        SizedBox(height: 3.h),

                        // Date Picker Field
                        DatePickerField(
                          selectedDate: _selectedDate,
                          onDateChanged: _onDateChanged,
                        ),
                        SizedBox(height: 3.h),

                        // Description Input Field
                        DescriptionInputField(
                          controller: _descriptionController,
                          errorText: _descriptionError,
                          onChanged: (value) => _validateForm(),
                        ),
                        SizedBox(height: 3.h),

                        // Category Selector
                        CategorySelector(
                            selectedCategory: _selectedCategory,
                            isIncome: _isIncome,
                            onCategorySelected: _onCategorySelected,
                            categories: _categories),
                        SizedBox(height: 3.h),

                        // Payment Mode Selector
                        PaymentModeSelector(
                            selectedPaymentMode: _selectedPaymentMode,
                            onPaymentModeSelected: _onPaymentModeSelected,
                            paymentModes: _paymentModes),
                        SizedBox(height: 3.h),

                        // Receipt Attachment Widget
                        ReceiptAttachmentWidget(
                          attachedImages: _attachedImages,
                          onImagesChanged: _onImagesChanged,
                        ),
                        SizedBox(height: 4.h),
                      ],
                    ),
                  ),
                ),
              ),

              // Save Transaction Button
              Container(
                width: double.infinity,
                padding: EdgeInsets.all(4.w),
                decoration: BoxDecoration(
                  color: theme.colorScheme.surface,
                  border: Border(
                    top: BorderSide(
                      color: isDark
                          ? const Color(0xFF424242)
                          : const Color(0xFFE0E0E0),
                      width: 1.0,
                    ),
                  ),
                ),
                child: ElevatedButton(
                  onPressed: _isFormValid ? _saveTransaction : null,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: _isFormValid
                        ? (_isIncome
                            ? AppTheme.getSuccessColor(isDark)
                            : AppTheme.getErrorColor(isDark))
                        : (isDark
                            ? const Color(0xFF424242)
                            : const Color(0xFFE0E0E0)),
                    foregroundColor: _isFormValid
                        ? Colors.white
                        : (isDark
                            ? const Color(0xFF616161)
                            : const Color(0xFFBDBDBD)),
                    elevation: _isFormValid ? 2.0 : 0.0,
                    padding: EdgeInsets.symmetric(vertical: 2.h),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8.0),
                    ),
                  ),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      CustomIconWidget(
                        iconName: 'save',
                        color: _isFormValid
                            ? Colors.white
                            : (isDark
                                ? const Color(0xFF616161)
                                : const Color(0xFFBDBDBD)),
                        size: 20,
                      ),
                      SizedBox(width: 2.w),
                      Text(
                        _isEditMode ? 'Update Transaction' : 'Save Transaction',
                        style: GoogleFonts.inter(
                          fontSize: 16.sp,
                          fontWeight: FontWeight.w600,
                          letterSpacing: 0.1,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
