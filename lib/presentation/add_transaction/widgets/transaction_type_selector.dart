import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';
import 'package:google_fonts/google_fonts.dart';

import '../../../core/app_export.dart';
import '../../../theme/app_theme.dart';
import '../../../widgets/custom_icon_widget.dart';

class TransactionTypeSelector extends StatelessWidget {
  final bool isIncome;
  final ValueChanged<bool> onTypeChanged;

  const TransactionTypeSelector({
    super.key,
    required this.isIncome,
    required this.onTypeChanged,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    return Container(
      width: double.infinity,
      height: 6.h,
      decoration: BoxDecoration(
        color: isDark ? const Color(0xFF2D2D2D) : const Color(0xFFF5F5F5),
        borderRadius: BorderRadius.circular(8.0),
      ),
      padding: EdgeInsets.all(0.5.h),
      child: Row(
        children: [
          Expanded(
            child: GestureDetector(
              onTap: () => onTypeChanged(true),
              child: AnimatedContainer(
                duration: const Duration(milliseconds: 200),
                decoration: BoxDecoration(
                  color: isIncome
                      ? AppTheme.getSuccessColor(isDark)
                      : Colors.transparent,
                  borderRadius: BorderRadius.circular(6.0),
                ),
                child: Center(
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      CustomIconWidget(
                        iconName: 'trending_up',
                        color: isIncome
                            ? Colors.white
                            : (isDark
                                ? const Color(0xFFB0B0B0)
                                : const Color(0xFF757575)),
                        size: 20,
                      ),
                      SizedBox(width: 2.w),
                      Text(
                        'Income',
                        style: GoogleFonts.inter(
                          fontSize: 14.sp,
                          fontWeight:
                              isIncome ? FontWeight.w600 : FontWeight.w400,
                          color: isIncome
                              ? Colors.white
                              : (isDark
                                  ? const Color(0xFFB0B0B0)
                                  : const Color(0xFF757575)),
                          letterSpacing: 0.1,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),
          Expanded(
            child: GestureDetector(
              onTap: () => onTypeChanged(false),
              child: AnimatedContainer(
                duration: const Duration(milliseconds: 200),
                decoration: BoxDecoration(
                  color: !isIncome
                      ? AppTheme.getErrorColor(isDark)
                      : Colors.transparent,
                  borderRadius: BorderRadius.circular(6.0),
                ),
                child: Center(
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      CustomIconWidget(
                        iconName: 'trending_down',
                        color: !isIncome
                            ? Colors.white
                            : (isDark
                                ? const Color(0xFFB0B0B0)
                                : const Color(0xFF757575)),
                        size: 20,
                      ),
                      SizedBox(width: 2.w),
                      Text(
                        'Expense',
                        style: GoogleFonts.inter(
                          fontSize: 14.sp,
                          fontWeight:
                              !isIncome ? FontWeight.w600 : FontWeight.w400,
                          color: !isIncome
                              ? Colors.white
                              : (isDark
                                  ? const Color(0xFFB0B0B0)
                                  : const Color(0xFF757575)),
                          letterSpacing: 0.1,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}