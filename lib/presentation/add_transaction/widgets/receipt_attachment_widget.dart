import 'package:camera/camera.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:image_picker/image_picker.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:sizer/sizer.dart';

import '../../../core/app_export.dart';
import '../../../theme/app_theme.dart';
import '../../../widgets/custom_icon_widget.dart';
import '../../../widgets/custom_image_widget.dart';

class ReceiptAttachmentWidget extends StatefulWidget {
  final List<XFile> attachedImages;
  final ValueChanged<List<XFile>> onImagesChanged;

  const ReceiptAttachmentWidget({
    super.key,
    required this.attachedImages,
    required this.onImagesChanged,
  });

  @override
  State<ReceiptAttachmentWidget> createState() =>
      _ReceiptAttachmentWidgetState();
}

class _ReceiptAttachmentWidgetState extends State<ReceiptAttachmentWidget> {
  List<CameraDescription> _cameras = [];
  CameraController? _cameraController;
  final ImagePicker _imagePicker = ImagePicker();
  bool _isCameraInitialized = false;
  bool _isInitializing = false;

  @override
  void initState() {
    super.initState();
    _initializeCamera();
  }

  @override
  void dispose() {
    _cameraController?.dispose();
    super.dispose();
  }

  Future<bool> _requestCameraPermission() async {
    if (kIsWeb) return true;
    return (await Permission.camera.request()).isGranted;
  }

  Future<void> _initializeCamera() async {
    if (_isInitializing) return;
    _isInitializing = true;

    try {
      if (await _requestCameraPermission()) {
        _cameras = await availableCameras();
        if (_cameras.isNotEmpty) {
          final camera = kIsWeb
              ? _cameras.firstWhere(
                  (c) => c.lensDirection == CameraLensDirection.front,
                  orElse: () => _cameras.first)
              : _cameras.firstWhere(
                  (c) => c.lensDirection == CameraLensDirection.back,
                  orElse: () => _cameras.first);

          _cameraController = CameraController(
              camera, kIsWeb ? ResolutionPreset.medium : ResolutionPreset.high);

          await _cameraController!.initialize();
          await _applySettings();

          if (mounted) {
            setState(() {
              _isCameraInitialized = true;
            });
          }
        }
      }
    } catch (e) {
      debugPrint('Camera initialization error: $e');
    } finally {
      _isInitializing = false;
    }
  }

  Future<void> _applySettings() async {
    if (_cameraController == null) return;

    try {
      await _cameraController!.setFocusMode(FocusMode.auto);
      if (!kIsWeb) {
        try {
          await _cameraController!.setFlashMode(FlashMode.auto);
        } catch (e) {
          debugPrint('Flash mode not supported: $e');
        }
      }
    } catch (e) {
      debugPrint('Camera settings error: $e');
    }
  }

  Future<void> _capturePhoto() async {
    if (_cameraController == null || !_isCameraInitialized) return;

    try {
      final XFile photo = await _cameraController!.takePicture();
      final updatedImages = List<XFile>.from(widget.attachedImages)..add(photo);
      widget.onImagesChanged(updatedImages);
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Failed to capture photo')),
        );
      }
    }
  }

  Future<void> _pickFromGallery() async {
    try {
      final XFile? image = await _imagePicker.pickImage(
        source: ImageSource.gallery,
        imageQuality: 80,
      );

      if (image != null) {
        final updatedImages = List<XFile>.from(widget.attachedImages)
          ..add(image);
        widget.onImagesChanged(updatedImages);
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Failed to pick image from gallery')),
        );
      }
    }
  }

  void _removeImage(int index) {
    final updatedImages = List<XFile>.from(widget.attachedImages)
      ..removeAt(index);
    widget.onImagesChanged(updatedImages);
  }

  void _showImageOptions() {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        decoration: BoxDecoration(
          color: theme.colorScheme.surface,
          borderRadius: const BorderRadius.only(
            topLeft: Radius.circular(20.0),
            topRight: Radius.circular(20.0),
          ),
        ),
        child: SafeArea(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Container(
                width: 12.w,
                height: 0.5.h,
                margin: EdgeInsets.only(top: 1.h),
                decoration: BoxDecoration(
                  color: isDark
                      ? const Color(0xFF424242)
                      : const Color(0xFFE0E0E0),
                  borderRadius: BorderRadius.circular(2.0),
                ),
              ),
              Padding(
                padding: EdgeInsets.all(4.w),
                child: Text(
                  'Add Receipt Photo',
                  style: GoogleFonts.inter(
                    fontSize: 18.sp,
                    fontWeight: FontWeight.w600,
                    color: isDark ? Colors.white : const Color(0xFF212121),
                  ),
                ),
              ),
              ListTile(
                leading: CustomIconWidget(
                  iconName: 'camera_alt',
                  color: isDark
                      ? const Color(0xFF4A90E2)
                      : const Color(0xFF1B365D),
                  size: 24,
                ),
                title: Text(
                  'Take Photo',
                  style: GoogleFonts.inter(
                    fontSize: 16.sp,
                    fontWeight: FontWeight.w500,
                    color: isDark ? Colors.white : const Color(0xFF212121),
                  ),
                ),
                onTap: () {
                  Navigator.pop(context);
                  _capturePhoto();
                },
              ),
              ListTile(
                leading: CustomIconWidget(
                  iconName: 'photo_library',
                  color: isDark
                      ? const Color(0xFF4A90E2)
                      : const Color(0xFF1B365D),
                  size: 24,
                ),
                title: Text(
                  'Choose from Gallery',
                  style: GoogleFonts.inter(
                    fontSize: 16.sp,
                    fontWeight: FontWeight.w500,
                    color: isDark ? Colors.white : const Color(0xFF212121),
                  ),
                ),
                onTap: () {
                  Navigator.pop(context);
                  _pickFromGallery();
                },
              ),
              SizedBox(height: 2.h),
            ],
          ),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Receipt Attachment',
          style: GoogleFonts.inter(
            fontSize: 14.sp,
            fontWeight: FontWeight.w500,
            color: isDark ? Colors.white : const Color(0xFF212121),
            letterSpacing: 0.1,
          ),
        ),
        SizedBox(height: 1.h),
        GestureDetector(
          onTap: _showImageOptions,
          child: Container(
            width: double.infinity,
            padding: EdgeInsets.symmetric(horizontal: 4.w, vertical: 2.h),
            decoration: BoxDecoration(
              color: theme.colorScheme.surface,
              borderRadius: BorderRadius.circular(8.0),
              border: Border.all(
                color:
                    isDark ? const Color(0xFF424242) : const Color(0xFFE0E0E0),
                width: 1.0,
                style: BorderStyle.solid,
              ),
            ),
            child: Row(
              children: [
                CustomIconWidget(
                  iconName: 'camera_alt',
                  color: isDark
                      ? const Color(0xFF4A90E2)
                      : const Color(0xFF1B365D),
                  size: 20,
                ),
                SizedBox(width: 3.w),
                Expanded(
                  child: Text(
                    widget.attachedImages.isEmpty
                        ? 'Add receipt photo'
                        : '${widget.attachedImages.length} photo${widget.attachedImages.length > 1 ? 's' : ''} attached',
                    style: GoogleFonts.inter(
                      fontSize: 16.sp,
                      fontWeight: FontWeight.w400,
                      color: widget.attachedImages.isNotEmpty
                          ? (isDark ? Colors.white : const Color(0xFF212121))
                          : (isDark
                              ? const Color(0xFF616161)
                              : const Color(0xFFBDBDBD)),
                    ),
                  ),
                ),
                CustomIconWidget(
                  iconName: 'add',
                  color: isDark
                      ? const Color(0xFFB0B0B0)
                      : const Color(0xFF757575),
                  size: 20,
                ),
              ],
            ),
          ),
        ),
        if (widget.attachedImages.isNotEmpty) ...[
          SizedBox(height: 2.h),
          SizedBox(
            height: 20.h,
            child: ListView.builder(
              scrollDirection: Axis.horizontal,
              itemCount: widget.attachedImages.length,
              itemBuilder: (context, index) {
                final image = widget.attachedImages[index];
                return Container(
                  width: 30.w,
                  margin: EdgeInsets.only(right: 2.w),
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(8.0),
                    border: Border.all(
                      color: isDark
                          ? const Color(0xFF424242)
                          : const Color(0xFFE0E0E0),
                    ),
                  ),
                  child: Stack(
                    children: [
                      ClipRRect(
                        borderRadius: BorderRadius.circular(8.0),
                        child: kIsWeb
                            ? Image.network(
                                image.path,
                                width: 30.w,
                                height: 20.h,
                                fit: BoxFit.cover,
                                errorBuilder: (context, error, stackTrace) {
                                  return Container(
                                    width: 30.w,
                                    height: 20.h,
                                    color: isDark
                                        ? const Color(0xFF2D2D2D)
                                        : const Color(0xFFF5F5F5),
                                    child: CustomIconWidget(
                                      iconName: 'image',
                                      color: isDark
                                          ? const Color(0xFF616161)
                                          : const Color(0xFFBDBDBD),
                                      size: 32,
                                    ),
                                  );
                                },
                              )
                            : CustomImageWidget(
                                imageUrl: image.path,
                                width: 30.w,
                                height: 20.h,
                                fit: BoxFit.cover,
                              ),
                      ),
                      Positioned(
                        top: 1.h,
                        right: 2.w,
                        child: GestureDetector(
                          onTap: () => _removeImage(index),
                          child: Container(
                            padding: EdgeInsets.all(0.5.h),
                            decoration: BoxDecoration(
                              color: AppTheme.getErrorColor(isDark),
                              shape: BoxShape.circle,
                            ),
                            child: CustomIconWidget(
                              iconName: 'close',
                              color: Colors.white,
                              size: 16,
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                );
              },
            ),
          ),
        ],
      ],
    );
  }
}