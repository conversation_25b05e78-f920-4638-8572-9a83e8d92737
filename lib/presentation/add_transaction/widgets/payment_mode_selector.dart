import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';
import 'package:google_fonts/google_fonts.dart';

import '../../../core/app_export.dart';
import '../../../widgets/custom_icon_widget.dart';

class PaymentModeSelector extends StatelessWidget {
  final PaymentModeModel? selectedPaymentMode;
  final ValueChanged<PaymentModeModel> onPaymentModeSelected;
  final List<PaymentModeModel> paymentModes;

  const PaymentModeSelector(
      {super.key,
      required this.selectedPaymentMode,
      required this.onPaymentModeSelected,
      required this.paymentModes});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Payment Mode',
          style: GoogleFonts.inter(
            fontSize: 14.sp,
            fontWeight: FontWeight.w500,
            color: isDark ? Colors.white : const Color(0xFF212121),
            letterSpacing: 0.1,
          ),
        ),
        SizedBox(height: 1.h),
        GestureDetector(
          onTap: () => _showPaymentModeBottomSheet(context, paymentModes),
          child: Container(
            width: double.infinity,
            padding: EdgeInsets.symmetric(horizontal: 4.w, vertical: 2.h),
            decoration: BoxDecoration(
              color: theme.colorScheme.surface,
              borderRadius: BorderRadius.circular(8.0),
              border: Border.all(
                color:
                    isDark ? const Color(0xFF424242) : const Color(0xFFE0E0E0),
                width: 1.0,
              ),
            ),
            child: Row(
              children: [
                CustomIconWidget(
                  iconName: selectedPaymentMode?.icon ?? 'money',
                  color: isDark
                      ? const Color(0xFF4A90E2)
                      : const Color(0xFF1B365D),
                  size: 20,
                ),
                SizedBox(width: 3.w),
                Expanded(
                  child: Text(
                    selectedPaymentMode?.name ?? 'Select Payment Method',
                    style: GoogleFonts.inter(
                      fontSize: 16.sp,
                      fontWeight: FontWeight.w400,
                      color: selectedPaymentMode != null
                          ? (isDark ? Colors.white : const Color(0xFF212121))
                          : (isDark
                              ? const Color(0xFF616161)
                              : const Color(0xFFBDBDBD)),
                    ),
                  ),
                ),
                CustomIconWidget(
                  iconName: 'keyboard_arrow_down',
                  color: isDark
                      ? const Color(0xFFB0B0B0)
                      : const Color(0xFF757575),
                  size: 20,
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  void _showPaymentModeBottomSheet(
      BuildContext context, List<PaymentModeModel> paymentModes) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        height: 60.h,
        decoration: BoxDecoration(
          color: theme.colorScheme.surface,
          borderRadius: const BorderRadius.only(
            topLeft: Radius.circular(20.0),
            topRight: Radius.circular(20.0),
          ),
        ),
        child: Column(
          children: [
            Container(
              width: 12.w,
              height: 0.5.h,
              margin: EdgeInsets.only(top: 1.h),
              decoration: BoxDecoration(
                color:
                    isDark ? const Color(0xFF424242) : const Color(0xFFE0E0E0),
                borderRadius: BorderRadius.circular(2.0),
              ),
            ),
            Padding(
              padding: EdgeInsets.all(4.w),
              child: Row(
                children: [
                  Text(
                    'Select Payment Mode',
                    style: GoogleFonts.inter(
                      fontSize: 18.sp,
                      fontWeight: FontWeight.w600,
                      color: isDark ? Colors.white : const Color(0xFF212121),
                    ),
                  ),
                  const Spacer(),
                  GestureDetector(
                    onTap: () => Navigator.pop(context),
                    child: CustomIconWidget(
                      iconName: 'close',
                      color: isDark
                          ? const Color(0xFFB0B0B0)
                          : const Color(0xFF757575),
                      size: 24,
                    ),
                  ),
                ],
              ),
            ),
            Expanded(
              child: ListView.builder(
                padding: EdgeInsets.symmetric(horizontal: 4.w),
                itemCount: paymentModes.length + 1,
                itemBuilder: (context, index) {
                  if (index == paymentModes.length) {
                    return Padding(
                      padding: EdgeInsets.only(bottom: 2.h),
                      child: GestureDetector(
                        onTap: () {
                          Navigator.pop(context);
                          _showAddCustomPaymentModeDialog(context);
                        },
                        child: Container(
                          padding: EdgeInsets.symmetric(
                              horizontal: 4.w, vertical: 2.h),
                          decoration: BoxDecoration(
                            color: (isDark
                                    ? const Color(0xFF4A90E2)
                                    : const Color(0xFF1B365D))
                                .withValues(alpha: 0.1),
                            borderRadius: BorderRadius.circular(8.0),
                            border: Border.all(
                              color: isDark
                                  ? const Color(0xFF4A90E2)
                                  : const Color(0xFF1B365D),
                              style: BorderStyle.solid,
                            ),
                          ),
                          child: Row(
                            children: [
                              CustomIconWidget(
                                iconName: 'add',
                                color: isDark
                                    ? const Color(0xFF4A90E2)
                                    : const Color(0xFF1B365D),
                                size: 24,
                              ),
                              SizedBox(width: 3.w),
                              Text(
                                'Add Custom Payment Mode',
                                style: GoogleFonts.inter(
                                  fontSize: 16.sp,
                                  fontWeight: FontWeight.w500,
                                  color: isDark
                                      ? const Color(0xFF4A90E2)
                                      : const Color(0xFF1B365D),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    );
                  }

                  final paymentMode = paymentModes[index];
                  final isSelected = selectedPaymentMode == paymentMode.name;

                  return Padding(
                    padding: EdgeInsets.only(bottom: 1.h),
                    child: GestureDetector(
                      onTap: () {
                        onPaymentModeSelected(paymentMode);
                        Navigator.pop(context);
                      },
                      child: Container(
                        padding: EdgeInsets.symmetric(
                            horizontal: 4.w, vertical: 2.h),
                        decoration: BoxDecoration(
                          color: isSelected
                              ? (isDark
                                      ? const Color(0xFF4A90E2)
                                      : const Color(0xFF1B365D))
                                  .withValues(alpha: 0.1)
                              : theme.colorScheme.surface,
                          borderRadius: BorderRadius.circular(8.0),
                          border: Border.all(
                            color: isSelected
                                ? (isDark
                                    ? const Color(0xFF4A90E2)
                                    : const Color(0xFF1B365D))
                                : (isDark
                                    ? const Color(0xFF424242)
                                    : const Color(0xFFE0E0E0)),
                          ),
                        ),
                        child: Row(
                          children: [
                            CustomIconWidget(
                              iconName: paymentMode.icon.toString(),
                              color: isSelected
                                  ? (isDark
                                      ? const Color(0xFF4A90E2)
                                      : const Color(0xFF1B365D))
                                  : (isDark
                                      ? const Color(0xFFB0B0B0)
                                      : const Color(0xFF757575)),
                              size: 24,
                            ),
                            SizedBox(width: 3.w),
                            Text(
                              paymentMode.name,
                              style: GoogleFonts.inter(
                                fontSize: 16.sp,
                                fontWeight: FontWeight.w500,
                                color: isSelected
                                    ? (isDark
                                        ? const Color(0xFF4A90E2)
                                        : const Color(0xFF1B365D))
                                    : (isDark
                                        ? Colors.white
                                        : const Color(0xFF212121)),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _showAddCustomPaymentModeDialog(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;
    final TextEditingController controller = TextEditingController();

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: theme.colorScheme.surface,
        title: Text(
          'Add Custom Payment Mode',
          style: GoogleFonts.inter(
            fontSize: 18.sp,
            fontWeight: FontWeight.w600,
            color: isDark ? Colors.white : const Color(0xFF212121),
          ),
        ),
        content: TextFormField(
          controller: controller,
          autofocus: true,
          style: GoogleFonts.inter(
            fontSize: 16.sp,
            fontWeight: FontWeight.w400,
            color: isDark ? Colors.white : const Color(0xFF212121),
          ),
          decoration: InputDecoration(
            hintText: 'Enter payment mode name',
            hintStyle: GoogleFonts.inter(
              fontSize: 16.sp,
              fontWeight: FontWeight.w400,
              color: isDark ? const Color(0xFF616161) : const Color(0xFFBDBDBD),
            ),
            fillColor: theme.colorScheme.surface,
            filled: true,
            contentPadding:
                EdgeInsets.symmetric(horizontal: 4.w, vertical: 2.h),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8.0),
              borderSide: BorderSide(
                color:
                    isDark ? const Color(0xFF424242) : const Color(0xFFE0E0E0),
              ),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8.0),
              borderSide: BorderSide(
                color:
                    isDark ? const Color(0xFF424242) : const Color(0xFFE0E0E0),
              ),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8.0),
              borderSide: BorderSide(
                color:
                    isDark ? const Color(0xFF4A90E2) : const Color(0xFF1B365D),
                width: 2.0,
              ),
            ),
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(
              'Cancel',
              style: GoogleFonts.inter(
                fontSize: 14.sp,
                fontWeight: FontWeight.w500,
                color:
                    isDark ? const Color(0xFFB0B0B0) : const Color(0xFF757575),
              ),
            ),
          ),
          ElevatedButton(
            onPressed: () {
              if (controller.text.trim().isNotEmpty) {
                onPaymentModeSelected(paymentModes
                    .singleWhere((p) => p.name == controller.text.trim()));
                Navigator.pop(context);
              }
            },
            style: ElevatedButton.styleFrom(
              backgroundColor:
                  isDark ? const Color(0xFF4A90E2) : const Color(0xFF1B365D),
              foregroundColor: Colors.white,
            ),
            child: Text(
              'Add',
              style: GoogleFonts.inter(
                fontSize: 14.sp,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
