import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';
import 'package:google_fonts/google_fonts.dart';

import '../../../core/app_export.dart';
import '../../../theme/app_theme.dart';

class DescriptionInputField extends StatefulWidget {
  final TextEditingController controller;
  final String? errorText;
  final ValueChanged<String>? onChanged;

  const DescriptionInputField({
    super.key,
    required this.controller,
    this.errorText,
    this.onChanged,
  });

  @override
  State<DescriptionInputField> createState() => _DescriptionInputFieldState();
}

class _DescriptionInputFieldState extends State<DescriptionInputField> {
  final List<String> _suggestions = [
    'Grocery shopping',
    'Fuel expense',
    'Restaurant bill',
    'Office supplies',
    'Transportation',
    'Utilities payment',
    'Medical expenses',
    'Entertainment',
    'Salary received',
    'Freelance payment',
    'Investment return',
    'Bonus received',
  ];

  List<String> _filteredSuggestions = [];
  bool _showSuggestions = false;

  @override
  void initState() {
    super.initState();
    widget.controller.addListener(_onTextChanged);
  }

  @override
  void dispose() {
    widget.controller.removeListener(_onTextChanged);
    super.dispose();
  }

  void _onTextChanged() {
    final text = widget.controller.text.toLowerCase();
    if (text.isNotEmpty) {
      setState(() {
        _filteredSuggestions = _suggestions
            .where((suggestion) => suggestion.toLowerCase().contains(text))
            .take(3)
            .toList();
        _showSuggestions = _filteredSuggestions.isNotEmpty;
      });
    } else {
      setState(() {
        _showSuggestions = false;
        _filteredSuggestions.clear();
      });
    }

    if (widget.onChanged != null) {
      widget.onChanged!(widget.controller.text);
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Description',
          style: GoogleFonts.inter(
            fontSize: 14.sp,
            fontWeight: FontWeight.w500,
            color: isDark ? Colors.white : const Color(0xFF212121),
            letterSpacing: 0.1,
          ),
        ),
        SizedBox(height: 1.h),
        TextFormField(
          controller: widget.controller,
          maxLines: 2,
          style: GoogleFonts.inter(
            fontSize: 16.sp,
            fontWeight: FontWeight.w400,
            color: isDark ? Colors.white : const Color(0xFF212121),
          ),
          decoration: InputDecoration(
            hintText: 'Enter transaction description',
            hintStyle: GoogleFonts.inter(
              fontSize: 16.sp,
              fontWeight: FontWeight.w400,
              color: isDark ? const Color(0xFF616161) : const Color(0xFFBDBDBD),
            ),
            fillColor: theme.colorScheme.surface,
            filled: true,
            contentPadding:
                EdgeInsets.symmetric(horizontal: 4.w, vertical: 2.h),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8.0),
              borderSide: BorderSide(
                color: widget.errorText != null
                    ? AppTheme.getErrorColor(isDark)
                    : (isDark
                        ? const Color(0xFF424242)
                        : const Color(0xFFE0E0E0)),
              ),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8.0),
              borderSide: BorderSide(
                color: widget.errorText != null
                    ? AppTheme.getErrorColor(isDark)
                    : (isDark
                        ? const Color(0xFF424242)
                        : const Color(0xFFE0E0E0)),
              ),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8.0),
              borderSide: BorderSide(
                color: widget.errorText != null
                    ? AppTheme.getErrorColor(isDark)
                    : (isDark
                        ? const Color(0xFF4A90E2)
                        : const Color(0xFF1B365D)),
                width: 2.0,
              ),
            ),
            errorBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8.0),
              borderSide: BorderSide(color: AppTheme.getErrorColor(isDark)),
            ),
            focusedErrorBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8.0),
              borderSide:
                  BorderSide(color: AppTheme.getErrorColor(isDark), width: 2.0),
            ),
          ),
        ),
        if (_showSuggestions) ...[
          SizedBox(height: 1.h),
          Container(
            decoration: BoxDecoration(
              color: theme.colorScheme.surface,
              borderRadius: BorderRadius.circular(8.0),
              border: Border.all(
                color:
                    isDark ? const Color(0xFF424242) : const Color(0xFFE0E0E0),
              ),
            ),
            child: Column(
              children: _filteredSuggestions.map((suggestion) {
                return ListTile(
                  dense: true,
                  title: Text(
                    suggestion,
                    style: GoogleFonts.inter(
                      fontSize: 14.sp,
                      fontWeight: FontWeight.w400,
                      color: isDark ? Colors.white : const Color(0xFF212121),
                    ),
                  ),
                  onTap: () {
                    widget.controller.text = suggestion;
                    setState(() {
                      _showSuggestions = false;
                    });
                    if (widget.onChanged != null) {
                      widget.onChanged!(suggestion);
                    }
                  },
                );
              }).toList(),
            ),
          ),
        ],
        if (widget.errorText != null) ...[
          SizedBox(height: 0.5.h),
          Text(
            widget.errorText!,
            style: GoogleFonts.inter(
              fontSize: 12.sp,
              fontWeight: FontWeight.w400,
              color: AppTheme.getErrorColor(isDark),
            ),
          ),
        ],
      ],
    );
  }
}