import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';

import '../../../core/app_export.dart';
import '../../../widgets/custom_icon_widget.dart';

class CashbookCreationModal extends StatefulWidget {
  final Function(Map<String, dynamic>) onCreateCashbook;

  const CashbookCreationModal({
    super.key,
    required this.onCreateCashbook,
  });

  @override
  State<CashbookCreationModal> createState() => _CashbookCreationModalState();
}

class _CashbookCreationModalState extends State<CashbookCreationModal> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _descriptionController = TextEditingController();

  String _selectedCurrency = 'MWK';
  int _selectedColorIndex = 0;
  String _selectedTemplate = '';

  final List<String> _currencies = [
    'MWK',
    'USD',
    'EUR',
    'GBP',
    'JPY',
    'CAD',
    'AUD'
  ];

  final List<Color> _colors = [
    const Color(0xFF1B365D),
    const Color(0xFF2E7D32),
    const Color(0xFFF57C00),
    const Color(0xFFC62828),
    const Color(0xFF7B1FA2),
    const Color(0xFF00796B),
    const Color(0xFF5D4037),
    const Color(0xFF455A64),
  ];

  final List<Map<String, dynamic>> _templates = [
    {
      'name': 'Retail Shop',
      'description': 'Perfect for retail businesses with daily sales tracking',
      'icon': 'store',
      'categories': ['Sales', 'Inventory', 'Utilities', 'Rent', 'Staff'],
    },
    {
      'name': 'Freelance Work',
      'description': 'Track project income and business expenses',
      'icon': 'work',
      'categories': ['Client Payments', 'Equipment', 'Software', 'Marketing'],
    },
    {
      'name': 'Personal Expenses',
      'description': 'Manage household and personal finances',
      'icon': 'person',
      'categories': ['Food', 'Transport', 'Entertainment', 'Bills', 'Shopping'],
    },
    {
      'name': 'Rental Property',
      'description': 'Track rental income and property expenses',
      'icon': 'home',
      'categories': ['Rent Income', 'Maintenance', 'Insurance', 'Taxes'],
    },
  ];

  @override
  void dispose() {
    _nameController.dispose();
    _descriptionController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Container(
      height: 85.h,
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
      ),
      child: Column(
        children: [
          _buildHeader(theme),
          Expanded(
            child: SingleChildScrollView(
              padding: EdgeInsets.all(4.w),
              child: Form(
                key: _formKey,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _buildBasicInfo(theme),
                    SizedBox(height: 3.h),
                    _buildColorSelection(theme),
                    SizedBox(height: 3.h),
                    _buildCurrencySelection(theme),
                    SizedBox(height: 4.h),
                    _buildActionButtons(theme),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildHeader(ThemeData theme) {
    return Container(
      padding: EdgeInsets.all(4.w),
      decoration: BoxDecoration(
        border: Border(
          bottom: BorderSide(
            color: theme.dividerColor,
            width: 1,
          ),
        ),
      ),
      child: Row(
        children: [
          Expanded(
            child: Text(
              'Create New Cashbook',
              style: theme.textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
          IconButton(
            onPressed: () => Navigator.pop(context),
            icon: CustomIconWidget(
              iconName: 'close',
              color: theme.colorScheme.onSurface,
              size: 6.w,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBasicInfo(ThemeData theme) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Basic Information',
          style: theme.textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        SizedBox(height: 2.h),
        TextFormField(
          controller: _nameController,
          decoration: const InputDecoration(
            labelText: 'Cashbook Name',
            hintText: 'Enter cashbook name',
          ),
          validator: (value) {
            if (value == null || value.trim().isEmpty) {
              return 'Please enter a cashbook name';
            }
            return null;
          },
        ),
        SizedBox(height: 2.h),
        TextFormField(
          controller: _descriptionController,
          decoration: const InputDecoration(
            labelText: 'Description (Optional)',
            hintText: 'Brief description of this cashbook',
          ),
          maxLines: 3,
        ),
      ],
    );
  }

  Widget _buildColorSelection(ThemeData theme) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Color Theme',
          style: theme.textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        SizedBox(height: 2.h),
        Wrap(
          spacing: 3.w,
          runSpacing: 2.h,
          children: List.generate(_colors.length, (index) {
            final isSelected = index == _selectedColorIndex;
            return GestureDetector(
              onTap: () {
                setState(() {
                  _selectedColorIndex = index;
                });
              },
              child: Container(
                width: 12.w,
                height: 6.h,
                decoration: BoxDecoration(
                  color: _colors[index],
                  borderRadius: BorderRadius.circular(8),
                  border: isSelected
                      ? Border.all(
                          color: theme.colorScheme.primary,
                          width: 3,
                        )
                      : null,
                ),
                child: isSelected
                    ? Center(
                        child: CustomIconWidget(
                          iconName: 'check',
                          color: Colors.white,
                          size: 5.w,
                        ),
                      )
                    : null,
              ),
            );
          }),
        ),
      ],
    );
  }

  Widget _buildCurrencySelection(ThemeData theme) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Currency',
          style: theme.textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        SizedBox(height: 2.h),
        DropdownButtonFormField<String>(
          value: _selectedCurrency,
          decoration: const InputDecoration(
            labelText: 'Select Currency',
          ),
          items: _currencies.map((currency) {
            return DropdownMenuItem(
              value: currency,
              child: Text(currency),
            );
          }).toList(),
          onChanged: (value) {
            if (value != null) {
              setState(() {
                _selectedCurrency = value;
              });
            }
          },
        ),
      ],
    );
  }

  Widget _buildActionButtons(ThemeData theme) {
    return Row(
      children: [
        Expanded(
          child: OutlinedButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
        ),
        SizedBox(width: 4.w),
        Expanded(
          child: ElevatedButton(
            onPressed: _createCashbook,
            child: const Text('Create Cashbook'),
          ),
        ),
      ],
    );
  }

  void _createCashbook() {
    if (_formKey.currentState?.validate() ?? false) {
      final selectedTemplate = _selectedTemplate.isNotEmpty
          ? _templates.firstWhere((t) => t['name'] == _selectedTemplate)
          : null;

      final newCashbook = {
        'id': DateTime.now().millisecondsSinceEpoch,
        'name': _nameController.text.trim(),
        'description': _descriptionController.text.trim().isEmpty
            ? 'No description provided'
            : _descriptionController.text.trim(),
        'currency': _selectedCurrency,
        'color':
            '0x${_colors[_selectedColorIndex].toARGB32().toRadixString(16).toUpperCase()}',
        'icon': selectedTemplate?['icon'] ?? 'account_balance_wallet',
        'balance': 0.0,
        'transactionCount': 0,
        'lastActivity': DateTime.now(),
        'isDefault': false,
        'template': _selectedTemplate,
        'categories': selectedTemplate?['categories'] ?? <String>[],
        'createdAt': DateTime.now(),
      };

      widget.onCreateCashbook(newCashbook);
      Navigator.pop(context);
    }
  }
}
