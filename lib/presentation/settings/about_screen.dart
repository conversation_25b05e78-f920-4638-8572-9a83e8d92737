import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';

import '../../core/app_export.dart';

class AboutScreen extends StatelessWidget {
  const AboutScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Scaffold(
      backgroundColor: theme.scaffoldBackgroundColor,
      appBar: AppBar(
        title: const Text('About CashBook'),
        backgroundColor: theme.appBarTheme.backgroundColor,
        elevation: 0,
      ),
      body: SingleChildScrollView(
        padding: EdgeInsets.all(4.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // App Logo and Info
            Center(
              child: Column(
                children: [
                  Container(
                    width: 25.w,
                    height: 25.w,
                    decoration: BoxDecoration(
                      color: theme.colorScheme.primary,
                      borderRadius: BorderRadius.circular(20),
                    ),
                    child: Icon(
                      Icons.account_balance_wallet,
                      color: theme.colorScheme.onPrimary,
                      size: 12.w,
                    ),
                  ),
                  SizedBox(height: 2.h),
                  Text(
                    'CashBook',
                    style: theme.textTheme.headlineMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: theme.colorScheme.primary,
                    ),
                  ),
                  Text(
                    'Version 1.0.0',
                    style: theme.textTheme.bodyMedium?.copyWith(
                      color: theme.colorScheme.onSurface.withValues(alpha: 0.6),
                    ),
                  ),
                ],
              ),
            ),
            SizedBox(height: 4.h),

            // About Section
            Text(
              'About',
              style: theme.textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            SizedBox(height: 2.h),
            Text(
              'CashBook is a comprehensive financial management application designed to help individuals and businesses track their income, expenses, and manage multiple cashbooks efficiently.',
              style: theme.textTheme.bodyMedium?.copyWith(
                height: 1.5,
                color: theme.colorScheme.onSurface.withValues(alpha: 0.8),
              ),
            ),
            SizedBox(height: 3.h),

            // Features Section
            Text(
              'Key Features',
              style: theme.textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            SizedBox(height: 2.h),
            _buildFeatureItem(
              theme: theme,
              icon: Icons.account_balance_wallet_outlined,
              title: 'Multiple Cashbooks',
              description: 'Manage multiple businesses or accounts separately',
            ),
            _buildFeatureItem(
              theme: theme,
              icon: Icons.analytics_outlined,
              title: 'Financial Reports',
              description: 'Generate detailed reports and analytics',
            ),
            _buildFeatureItem(
              theme: theme,
              icon: Icons.category_outlined,
              title: 'Category Management',
              description: 'Organize transactions with custom categories',
            ),
            _buildFeatureItem(
              theme: theme,
              icon: Icons.cloud_sync_outlined,
              title: 'Data Backup',
              description: 'Secure cloud backup and synchronization',
            ),
            SizedBox(height: 3.h),

            // Legal Section
            _buildLegalItem(
              theme: theme,
              title: 'Privacy Policy',
              onTap: () => _openPrivacyPolicy(context),
            ),
            _buildLegalItem(
              theme: theme,
              title: 'Terms & Conditions',
              onTap: () => _openTermsAndConditions(context),
            ),
            _buildLegalItem(
              theme: theme,
              title: 'Licenses',
              onTap: () => _openLicenses(context),
            ),
            SizedBox(height: 3.h),

            // Contact Section
            Text(
              'Contact & Support',
              style: theme.textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            SizedBox(height: 2.h),
            _buildContactItem(
              theme: theme,
              icon: Icons.email_outlined,
              title: 'Email Support',
              subtitle: '<EMAIL>',
              onTap: () => _contactSupport(context),
            ),
            _buildContactItem(
              theme: theme,
              icon: Icons.bug_report_outlined,
              title: 'Report a Bug',
              subtitle: 'Help us improve the app',
              onTap: () => _reportBug(context),
            ),
            _buildContactItem(
              theme: theme,
              icon: Icons.star_outline,
              title: 'Rate the App',
              subtitle: 'Share your feedback',
              onTap: () => _rateApp(context),
              showDivider: false,
            ),
            SizedBox(height: 4.h),

            // Copyright
            Center(
              child: Text(
                '© 2024 CashBook. All rights reserved.',
                style: theme.textTheme.bodySmall?.copyWith(
                  color: theme.colorScheme.onSurface.withValues(alpha: 0.5),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFeatureItem({
    required ThemeData theme,
    required IconData icon,
    required String title,
    required String description,
  }) {
    return Padding(
      padding: EdgeInsets.only(bottom: 2.h),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            width: 10.w,
            height: 10.w,
            decoration: BoxDecoration(
              color: theme.colorScheme.primary.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(
              icon,
              color: theme.colorScheme.primary,
              size: 5.w,
            ),
          ),
          SizedBox(width: 3.w),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: theme.textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.w500,
                  ),
                ),
                SizedBox(height: 0.5.h),
                Text(
                  description,
                  style: theme.textTheme.bodySmall?.copyWith(
                    color: theme.colorScheme.onSurface.withValues(alpha: 0.6),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildLegalItem({
    required ThemeData theme,
    required String title,
    required VoidCallback onTap,
  }) {
    return ListTile(
      title: Text(
        title,
        style: theme.textTheme.titleMedium?.copyWith(
          fontWeight: FontWeight.w500,
        ),
      ),
      trailing: Icon(
        Icons.arrow_forward_ios,
        size: 4.w,
        color: theme.colorScheme.onSurface.withValues(alpha: 0.4),
      ),
      onTap: onTap,
      contentPadding: EdgeInsets.symmetric(horizontal: 2.w, vertical: 0.5.h),
    );
  }

  Widget _buildContactItem({
    required ThemeData theme,
    required IconData icon,
    required String title,
    required String subtitle,
    required VoidCallback onTap,
    bool showDivider = true,
  }) {
    return Column(
      children: [
        ListTile(
          leading: Icon(
            icon,
            color: theme.colorScheme.primary,
            size: 6.w,
          ),
          title: Text(
            title,
            style: theme.textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.w500,
            ),
          ),
          subtitle: Text(
            subtitle,
            style: theme.textTheme.bodySmall?.copyWith(
              color: theme.colorScheme.onSurface.withValues(alpha: 0.6),
            ),
          ),
          trailing: Icon(
            Icons.arrow_forward_ios,
            size: 4.w,
            color: theme.colorScheme.onSurface.withValues(alpha: 0.4),
          ),
          onTap: onTap,
          contentPadding: EdgeInsets.symmetric(horizontal: 2.w, vertical: 1.h),
        ),
        if (showDivider)
          Divider(
            height: 1,
            color: theme.dividerColor.withValues(alpha: 0.1),
          ),
      ],
    );
  }

  void _openPrivacyPolicy(BuildContext context) {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Privacy Policy coming soon')),
    );
  }

  void _openTermsAndConditions(BuildContext context) {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Terms & Conditions coming soon')),
    );
  }

  void _openLicenses(BuildContext context) {
    showLicensePage(context: context);
  }

  void _contactSupport(BuildContext context) {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Email support coming soon')),
    );
  }

  void _reportBug(BuildContext context) {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Bug reporting coming soon')),
    );
  }

  void _rateApp(BuildContext context) {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('App rating coming soon')),
    );
  }
}
