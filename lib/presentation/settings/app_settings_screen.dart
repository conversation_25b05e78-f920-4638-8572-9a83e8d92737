import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:sizer/sizer.dart';

import '../../core/app_export.dart';
import '../../core/providers/theme_provider.dart';
import '../../core/services/preferences_service.dart';

class AppSettingsScreen extends StatefulWidget {
  const AppSettingsScreen({super.key});

  @override
  State<AppSettingsScreen> createState() => _AppSettingsScreenState();
}

class _AppSettingsScreenState extends State<AppSettingsScreen> {
  bool _notificationsEnabled = true;
  bool _biometricEnabled = false;
  bool _autoBackupEnabled = true;
  String _language = 'English';
  String _currency = 'USD';
  String _dateFormat = 'MM/dd/yyyy';

  @override
  void initState() {
    super.initState();
    _loadSettings();
  }

  void _loadSettings() {
    setState(() {
      _notificationsEnabled = PreferencesService.getNotificationsEnabled();
      _biometricEnabled = PreferencesService.getBiometricEnabled();
      _autoBackupEnabled = PreferencesService.getAutoBackupEnabled();
      _currency = PreferencesService.getDefaultCurrency();
      _dateFormat = PreferencesService.getDateFormat();
      // Language would be loaded from preferences when implemented
    });
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Scaffold(
      backgroundColor: theme.scaffoldBackgroundColor,
      appBar: AppBar(
        title: const Text('App Settings'),
        backgroundColor: theme.appBarTheme.backgroundColor,
        elevation: 0,
      ),
      body: SingleChildScrollView(
        padding: EdgeInsets.all(4.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Data Security Section
            _buildSectionHeader(theme, 'Data Security'),
            _buildDataBackupItem(theme),
            _buildAppLockItem(theme),
            SizedBox(height: 3.h),

            // Features Section
            _buildSectionHeader(theme, 'Features'),
            _buildNotificationsItem(theme),
            _buildCalculatorItem(theme),
            SizedBox(height: 3.h),

            // General Section
            _buildSectionHeader(theme, 'General'),
            _buildThemeItem(theme),
            _buildLanguageItem(theme),
            _buildCurrencyItem(theme),
            _buildDateFormatItem(theme),
          ],
        ),
      ),
    );
  }

  Widget _buildSectionHeader(ThemeData theme, String title) {
    return Padding(
      padding: EdgeInsets.only(bottom: 2.h),
      child: Text(
        title,
        style: theme.textTheme.titleSmall?.copyWith(
          color: theme.colorScheme.onSurface.withValues(alpha: 0.6),
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }

  Widget _buildDataBackupItem(ThemeData theme) {
    return _buildSettingsItem(
      theme: theme,
      icon: Icons.cloud_upload_outlined,
      title: 'Data Backup',
      onTap: () => Navigator.pushNamed(context, '/data-backup'),
    );
  }

  Widget _buildAppLockItem(ThemeData theme) {
    return _buildSwitchItem(
      theme: theme,
      icon: Icons.lock_outline,
      title: 'App Lock',
      value: _biometricEnabled,
      onChanged: (value) async {
        setState(() => _biometricEnabled = value);
        await PreferencesService.setBiometricEnabled(value);
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(value ? 'App lock enabled' : 'App lock disabled'),
              backgroundColor: AppTheme.getSuccessColor(
                  Theme.of(context).brightness == Brightness.dark),
            ),
          );
        }
      },
    );
  }

  Widget _buildNotificationsItem(ThemeData theme) {
    return _buildSwitchItem(
      theme: theme,
      icon: Icons.notifications_outlined,
      title: 'Group Book Notifications',
      value: _notificationsEnabled,
      onChanged: (value) async {
        setState(() => _notificationsEnabled = value);
        await PreferencesService.setNotificationsEnabled(value);
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                  value ? 'Notifications enabled' : 'Notifications disabled'),
              backgroundColor: AppTheme.getSuccessColor(
                  Theme.of(context).brightness == Brightness.dark),
            ),
          );
        }
      },
    );
  }

  Widget _buildCalculatorItem(ThemeData theme) {
    return _buildSwitchItem(
      theme: theme,
      icon: Icons.calculate_outlined,
      title: 'Amount Field Calculator',
      value: false, // This would be loaded from preferences
      onChanged: (value) {
        // TODO: Implement calculator feature toggle
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Calculator feature coming soon')),
        );
      },
    );
  }

  Widget _buildThemeItem(ThemeData theme) {
    return Consumer<ThemeProvider>(
      builder: (context, themeProvider, child) {
        return _buildSwitchItem(
          theme: theme,
          icon: Icons.dark_mode_outlined,
          title: 'Dark Theme',
          value: themeProvider.themeMode == ThemeMode.dark,
          onChanged: (value) async {
            final newThemeMode = value ? ThemeMode.dark : ThemeMode.light;
            await themeProvider.setThemeMode(newThemeMode);

            if (mounted) {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content:
                      Text('Theme changed to ${value ? 'dark' : 'light'} mode'),
                  backgroundColor: AppTheme.getSuccessColor(
                      Theme.of(context).brightness == Brightness.dark),
                ),
              );
            }
          },
        );
      },
    );
  }

  Widget _buildLanguageItem(ThemeData theme) {
    return _buildSettingsItem(
      theme: theme,
      icon: Icons.language_outlined,
      title: 'Language',
      subtitle: _language,
      onTap: () => _showLanguageDialog(),
    );
  }

  Widget _buildCurrencyItem(ThemeData theme) {
    return _buildSettingsItem(
      theme: theme,
      icon: Icons.attach_money_outlined,
      title: 'Default Currency',
      subtitle: _currency,
      onTap: () => _showCurrencyDialog(),
    );
  }

  Widget _buildDateFormatItem(ThemeData theme) {
    return _buildSettingsItem(
      theme: theme,
      icon: Icons.date_range_outlined,
      title: 'Date Format',
      subtitle: _dateFormat,
      onTap: () => _showDateFormatDialog(),
      showDivider: false,
    );
  }

  Widget _buildSettingsItem({
    required ThemeData theme,
    required IconData icon,
    required String title,
    String? subtitle,
    required VoidCallback onTap,
    bool showDivider = true,
  }) {
    return Column(
      children: [
        ListTile(
          leading: Icon(
            icon,
            color: theme.colorScheme.primary,
            size: 6.w,
          ),
          title: Text(
            title,
            style: theme.textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.w500,
            ),
          ),
          subtitle: subtitle != null
              ? Text(
                  subtitle,
                  style: theme.textTheme.bodySmall?.copyWith(
                    color: theme.colorScheme.onSurface.withValues(alpha: 0.6),
                  ),
                )
              : null,
          trailing: Icon(
            Icons.arrow_forward_ios,
            size: 4.w,
            color: theme.colorScheme.onSurface.withValues(alpha: 0.4),
          ),
          onTap: onTap,
          contentPadding: EdgeInsets.symmetric(horizontal: 2.w, vertical: 1.h),
        ),
        if (showDivider)
          Divider(
            height: 1,
            color: theme.dividerColor.withValues(alpha: 0.1),
          ),
      ],
    );
  }

  Widget _buildSwitchItem({
    required ThemeData theme,
    required IconData icon,
    required String title,
    required bool value,
    required ValueChanged<bool> onChanged,
    bool showDivider = true,
  }) {
    return Column(
      children: [
        ListTile(
          leading: Icon(
            icon,
            color: theme.colorScheme.primary,
            size: 6.w,
          ),
          title: Text(
            title,
            style: theme.textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.w500,
            ),
          ),
          trailing: Switch(
            value: value,
            onChanged: onChanged,
            activeColor: theme.colorScheme.primary,
          ),
          contentPadding: EdgeInsets.symmetric(horizontal: 2.w, vertical: 1.h),
        ),
        if (showDivider)
          Divider(
            height: 1,
            color: theme.dividerColor.withValues(alpha: 0.1),
          ),
      ],
    );
  }

  void _showLanguageDialog() {
    final languages = ['English', 'Spanish', 'French', 'German', 'Chinese'];

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Select Language'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: languages
              .map((lang) => RadioListTile<String>(
                    title: Text(lang),
                    value: lang,
                    groupValue: _language,
                    onChanged: (value) {
                      setState(() => _language = value!);
                      Navigator.pop(context);
                      // TODO: Implement language change
                    },
                  ))
              .toList(),
        ),
      ),
    );
  }

  void _showCurrencyDialog() {
    final currencies = ['USD', 'EUR', 'GBP', 'NGN', 'JPY', 'CAD', 'AUD'];

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Select Currency'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: currencies
              .map((curr) => RadioListTile<String>(
                    title: Text(curr),
                    value: curr,
                    groupValue: _currency,
                    onChanged: (value) async {
                      setState(() => _currency = value!);
                      await PreferencesService.setDefaultCurrency(value!);
                      Navigator.pop(context);
                    },
                  ))
              .toList(),
        ),
      ),
    );
  }

  void _showDateFormatDialog() {
    final formats = ['MM/dd/yyyy', 'dd/MM/yyyy', 'yyyy-MM-dd', 'dd-MM-yyyy'];

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Select Date Format'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: formats
              .map((format) => RadioListTile<String>(
                    title: Text(format),
                    value: format,
                    groupValue: _dateFormat,
                    onChanged: (value) async {
                      setState(() => _dateFormat = value!);
                      await PreferencesService.setDateFormat(value!);
                      Navigator.pop(context);
                    },
                  ))
              .toList(),
        ),
      ),
    );
  }
}
