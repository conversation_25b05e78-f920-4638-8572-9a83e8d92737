import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';

import '../../core/app_export.dart';

class BusinessSettingsScreen extends StatefulWidget {
  final Map<String, dynamic> cashbook;
  const BusinessSettingsScreen({super.key, required this.cashbook});

  @override
  State<BusinessSettingsScreen> createState() => _BusinessSettingsScreenState();
}

class _BusinessSettingsScreenState extends State<BusinessSettingsScreen> {
  late TextEditingController _nameController;
  late TextEditingController _descriptionController;
  String _currency = 'USD';
  bool _isDefault = false;
  bool _notificationsEnabled = true;

  @override
  void initState() {
    super.initState();
    _nameController =
        TextEditingController(text: widget.cashbook['name'] as String? ?? '');
    _descriptionController = TextEditingController(
        text: widget.cashbook['description'] as String? ?? '');
    _currency = widget.cashbook['currency'] as String? ?? 'USD';
    _isDefault = widget.cashbook['isDefault'] as bool? ?? false;
    // placeholder for a notification preference
    _notificationsEnabled = true;
  }

  @override
  void dispose() {
    _nameController.dispose();
    _descriptionController.dispose();
    super.dispose();
  }

  void _saveSettings() {
    // For now, we just show a SnackBar. Integrate with your state/store/backend as needed.
    final updated = {
      ...widget.cashbook,
      'name': _nameController.text,
      'description': _descriptionController.text,
      'currency': _currency,
      'isDefault': _isDefault,
    };

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Saved settings for "${updated['name']}"'),
        backgroundColor: AppTheme.getSuccessColor(
            Theme.of(context).brightness == Brightness.dark),
      ),
    );

    Navigator.pop(context, updated);
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Scaffold(
      appBar: AppBar(
        title: Text('Business Settings'),
      ),
      body: SafeArea(
        child: Padding(
          padding: EdgeInsets.all(4.w),
          child: Column(
            children: [
              Row(
                children: [
                  Container(
                    width: 14.w,
                    height: 14.w,
                    decoration: BoxDecoration(
                      color:
                          Color(int.parse(widget.cashbook['color'] as String)),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Center(
                      child: CustomIconWidget(
                        iconName: widget.cashbook['icon'] as String,
                        color: Colors.white,
                        size: 6.w,
                      ),
                    ),
                  ),
                  SizedBox(width: 4.w),
                  Expanded(
                    child: TextField(
                      controller: _nameController,
                      decoration: InputDecoration(labelText: 'Business name'),
                    ),
                  ),
                ],
              ),
              SizedBox(height: 3.h),
              TextField(
                controller: _descriptionController,
                decoration: InputDecoration(labelText: 'Description'),
                maxLines: 2,
              ),
              SizedBox(height: 2.h),
              Row(
                children: [
                  Expanded(
                    child: DropdownButtonFormField<String>(
                      value: _currency,
                      items: ['USD', 'EUR', 'NGN', 'GBP']
                          .map(
                              (c) => DropdownMenuItem(value: c, child: Text(c)))
                          .toList(),
                      onChanged: (v) =>
                          setState(() => _currency = v ?? _currency),
                      decoration: const InputDecoration(labelText: 'Currency'),
                    ),
                  ),
                  SizedBox(width: 4.w),
                  Expanded(
                    child: SwitchListTile(
                      title: const Text('Default cashbook'),
                      value: _isDefault,
                      onChanged: (v) => setState(() => _isDefault = v),
                      contentPadding: EdgeInsets.zero,
                    ),
                  ),
                ],
              ),
              SwitchListTile(
                title: const Text('Enable notifications'),
                value: _notificationsEnabled,
                onChanged: (v) => setState(() => _notificationsEnabled = v),
                contentPadding: EdgeInsets.zero,
              ),
              SizedBox(height: 3.h),

              // Team Management Section
              Container(
                padding: EdgeInsets.all(3.w),
                decoration: BoxDecoration(
                  color: theme.colorScheme.surface,
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(
                    color: theme.colorScheme.outline.withValues(alpha: 0.2),
                  ),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Team Management',
                      style: theme.textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    SizedBox(height: 1.h),
                    Text(
                      'Manage team members and their access to cashbooks',
                      style: theme.textTheme.bodySmall?.copyWith(
                        color:
                            theme.colorScheme.onSurface.withValues(alpha: 0.7),
                      ),
                    ),
                    SizedBox(height: 2.h),
                    ElevatedButton.icon(
                      onPressed: () {
                        Navigator.pushNamed(
                          context,
                          '/business-members',
                          arguments: {
                            'businessId': widget.cashbook['id'],
                            'businessName': widget.cashbook['name'],
                          },
                        );
                      },
                      icon: const Icon(Icons.people),
                      label: const Text('Manage Team Members'),
                      style: ElevatedButton.styleFrom(
                        minimumSize: Size(double.infinity, 5.h),
                      ),
                    ),
                  ],
                ),
              ),
              const Spacer(),
              Row(
                children: [
                  Expanded(
                    child: OutlinedButton(
                      onPressed: () => Navigator.pop(context),
                      child: const Text('Cancel'),
                    ),
                  ),
                  SizedBox(width: 4.w),
                  Expanded(
                    child: ElevatedButton(
                      onPressed: _saveSettings,
                      child: const Text('Save'),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
}
