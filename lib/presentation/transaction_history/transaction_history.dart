import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../../core/app_export.dart';
import '../../widgets/custom_tab_bar.dart';
import './widgets/bulk_actions_bar.dart';
import './widgets/cashbook_selector.dart';
import './widgets/filter_bottom_sheet.dart';
import './widgets/filter_chips.dart';
import './widgets/transaction_list.dart';
import './widgets/transaction_search_bar.dart';

class TransactionHistory extends StatefulWidget {
  const TransactionHistory({super.key});

  @override
  State<TransactionHistory> createState() => _TransactionHistoryState();
}

class _TransactionHistoryState extends State<TransactionHistory>
    with TickerProviderStateMixin {
  late TabController _tabController;

  // State variables
  String _searchQuery = '';
  String? _selectedCashbookId;
  Map<String, dynamic> _activeFilters = {};
  bool _isMultiSelectMode = false;
  List<String> _selectedTransactions = [];
  bool _isLoading = false;
  bool _hasMoreData = true;
  int _currentTabIndex = 0;
  String? _error;
  List<PaymentModeModel>? _paymentModes;
  List<CategoryModel>? _categories;

  // Data
  List<CashbookModel> _cashbooks = [];
  List<TransactionModel> _allTransactions = [];
  CashbookModel? _defaultCashbook;

  // Services
  late DataService _dataService;
  late AuthService _authService;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
    _tabController.addListener(_onTabChanged);
    _dataService = DataService.instance;
    _authService = AuthService.instance;
    _loadData();
  }

  @override
  void dispose() {
    _tabController.removeListener(_onTabChanged);
    _tabController.dispose();
    super.dispose();
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    _handleNavigationArguments();
    _loadData();
  }

  void _handleNavigationArguments() async {
    final args =
        ModalRoute.of(context)?.settings.arguments as Map<String, dynamic>;
    if (_selectedCashbookId != null) return;
    _selectedCashbookId =
        ((args.isNotEmpty) ? args['cashbook_id'] as String? : null);
  }

  Future<void> _loadData() async {
    if (!_authService.isAuthenticated) {
      Navigator.pushReplacementNamed(context, '/login');
      return;
    }

    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      if (_selectedCashbookId == null) return;
      // Load cashbooks
      _defaultCashbook =
          await _dataService.getCashbookById(_selectedCashbookId!);

      _cashbooks = await _dataService
          .getCashbooksByBusiness(_defaultCashbook!.businessId!);

      _paymentModes = await _dataService.getAllPaymentModes();
      _categories = await _dataService.getAllCategories();

      // Set selected cashbook
      if (_defaultCashbook != null) {
        _selectedCashbookId = _defaultCashbook!.id;
      } else if (_cashbooks.isNotEmpty) {
        _selectedCashbookId = _cashbooks.first.id;
      }

      // Load transactions for selected cashbook
      if (_selectedCashbookId != null) {
        await _loadTransactionsForCashbook(_selectedCashbookId!);
      }

      setState(() {
        _isLoading = false;
      });
    } catch (error) {
      setState(() {
        _error = error.toString();
        _isLoading = false;
      });
    }
  }

  Future<void> _loadTransactionsForCashbook(String cashbookId) async {
    try {
      final transactions = await _dataService.getCashbookTransactions(
        cashbookId: cashbookId,
        limit: 100, // Load more transactions for history
      );

      setState(() {
        _allTransactions = transactions;
      });
    } catch (error) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Failed to load transactions: $error')),
      );
    }
  }

  void _onTabChanged() {
    if (_tabController.indexIsChanging) {
      setState(() {
        _currentTabIndex = _tabController.index;
        _clearSelection();
      });
    }
  }

  // Convert models to maps for widget compatibility
  List<Map<String, dynamic>> get _cashbooksAsMap {
    return _cashbooks
        .map((cashbook) => {
              'name': cashbook.name,
              'balance': cashbook.balance,
              'color': cashbook.color != null
                  ? int.tryParse(cashbook.color!) ?? 0xFF1B365D
                  : 0xFF1B365D,
              'icon': cashbook.icon ?? 'account_balance_wallet',
            })
        .toList();
  }

  String _txnPaymentMode(String? paymentModeId) {
    if (paymentModeId == null) return 'Unknown';

    if (_paymentModes == null) return 'Unknown';

    PaymentModeModel mode =
        _paymentModes!.firstWhere((c) => c.id == paymentModeId);

    return mode.name;
  }

  String _txnCategory(String? categoryId) {
    if (categoryId == null) return 'Unknown';

    if (_categories == null) return 'Unknown';

    CategoryModel category = _categories!.firstWhere((c) => c.id == categoryId);

    return category.name;
  }

  List<Map<String, dynamic>> _getFilteredTransactions() {
    List<TransactionModel> filtered = List.from(_allTransactions);

    // Filter by tab
    switch (_currentTabIndex) {
      case 1: // Income
        filtered =
            filtered.where((t) => t.type == TransactionType.income).toList();
        break;
      case 2: // Expense
        filtered =
            filtered.where((t) => t.type == TransactionType.expense).toList();
        break;
      // case 3: // Transfer (not implemented yet)
      //   filtered = [];
      //   break;
      default: // All
        break;
    }

    // Filter by search query
    if (_searchQuery.isNotEmpty) {
      filtered = filtered.where((transaction) {
        final description = transaction.description.toLowerCase();
        final notes = (transaction.notes ?? '').toLowerCase();
        final query = _searchQuery.toLowerCase();
        return description.contains(query) || notes.contains(query);
      }).toList();
    }

    // Apply active filters
    if (_activeFilters['dateRange'] != null) {
      final dateRange = _activeFilters['dateRange'] as Map<String, DateTime>;
      final startDate = dateRange['start']!;
      final endDate = dateRange['end']!;

      filtered = filtered.where((transaction) {
        final date = transaction.transactionDate;
        return date.isAfter(startDate.subtract(const Duration(days: 1))) &&
            date.isBefore(endDate.add(const Duration(days: 1)));
      }).toList();
    }

    if (_activeFilters['categories'] != null) {
      final categories = _activeFilters['categories'] as List<String>;
      filtered = filtered.where((transaction) {
        return transaction.categoryId != null &&
            categories.contains(transaction.categoryId);
      }).toList();
    }

    if (_activeFilters['paymentModes'] != null) {
      final paymentModes = _activeFilters['paymentModes'] as List<String>;
      filtered = filtered.where((transaction) {
        return transaction.paymentModeId != null &&
            paymentModes.contains(transaction.paymentModeId);
      }).toList();
    }

    if (_activeFilters['amountRange'] != null) {
      final amountRange = _activeFilters['amountRange'] as Map<String, double>;
      final minAmount = amountRange['min']!;
      final maxAmount = amountRange['max']!;

      filtered = filtered.where((transaction) {
        final amount = transaction.amount;
        return amount >= minAmount && amount <= maxAmount;
      }).toList();
    }

    // Sort by date (newest first)
    filtered.sort((a, b) => b.transactionDate.compareTo(a.transactionDate));

    // Convert to map format for widget compatibility
    return filtered
        .map((txn) => {
              'id': txn.id,
              'amount': txn.amount,
              'type': txn.type.name,
              'description': txn.description,
              'category': _txnCategory(txn.categoryId),
              'paymentMode': _txnPaymentMode(txn.paymentModeId),
              'date': txn.transactionDate,
              'hasAttachment': false, // TODO: Check for attachments
            })
        .toList();
  }

  int _getActiveFiltersCount() {
    int count = 0;
    if (_activeFilters['dateRange'] != null) count++;
    if (_activeFilters['categories'] != null &&
        (_activeFilters['categories'] as List).isNotEmpty) count++;
    if (_activeFilters['paymentModes'] != null &&
        (_activeFilters['paymentModes'] as List).isNotEmpty) count++;
    if (_activeFilters['amountRange'] != null) count++;
    return count;
  }

  void _showFilterBottomSheet() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => FilterBottomSheet(
        currentFilters: _activeFilters,
        onFiltersChanged: (filters) {
          setState(() {
            _activeFilters = filters;
            _clearSelection();
          });
        },
      ),
    );
  }

  void _editTransaction(Map<String, dynamic> transaction) {
    Navigator.pushNamed(
      context,
      '/add-transaction',
      arguments: {
        'transactionId': transaction['id'],
        'mode': 'edit',
        'cashbookId': _selectedCashbookId,
      },
    );
  }

  void _duplicateTransaction(Map<String, dynamic> transaction) {
    Navigator.pushNamed(
      context,
      '/add-transaction',
      arguments: {
        'type': transaction['type'],
        'cashbookId': _selectedCashbookId,
        'amount': transaction['amount'],
        'description': '${transaction['description']} (Copy)',
      },
    );
  }

  Future<void> _deleteTransaction(Map<String, dynamic> transaction) async {
    try {
      await _dataService.deleteTransaction(transaction['id']);
      await _loadTransactionsForCashbook(_selectedCashbookId!);

      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Transaction deleted successfully'),
          duration: Duration(seconds: 2),
        ),
      );
    } catch (error) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Failed to delete transaction: $error')),
      );
    }
  }

  Future<void> _refreshTransactions() async {
    if (_selectedCashbookId != null) {
      await _loadTransactionsForCashbook(_selectedCashbookId!);
    }
  }

  void _loadMoreTransactions() {
    // TODO: Implement pagination
    setState(() {
      _isLoading = true;
    });

    Future.delayed(const Duration(seconds: 1), () {
      setState(() {
        _isLoading = false;
        _hasMoreData = false; // No more data to load for now
      });
    });
  }

  void _clearSelection() {
    setState(() {
      _selectedTransactions.clear();
      _isMultiSelectMode = false;
    });
  }

  Future<void> _bulkDeleteTransactions() async {
    try {
      for (final transactionId in _selectedTransactions) {
        await _dataService.deleteTransaction(transactionId);
      }

      await _loadTransactionsForCashbook(_selectedCashbookId!);
      _clearSelection();

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('${_selectedTransactions.length} transactions deleted'),
          duration: const Duration(seconds: 2),
        ),
      );
    } catch (error) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Failed to delete transactions: $error')),
      );
    }
  }

  void _bulkExportTransactions() {
    // TODO: Implement export functionality
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content:
            Text('Exporting ${_selectedTransactions.length} transactions...'),
        duration: const Duration(seconds: 2),
      ),
    );
    _clearSelection();
  }

  void _bulkCategorizeTransactions() {
    // TODO: Implement bulk categorization
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Change Category'),
        content:
            const Text('Select a new category for the selected transactions.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text(
                      'Updated ${_selectedTransactions.length} transactions'),
                  duration: const Duration(seconds: 2),
                ),
              );
              _clearSelection();
            },
            child: const Text('Update'),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    // Show loading state
    if (_isLoading && _allTransactions.isEmpty) {
      return Scaffold(
        backgroundColor: theme.scaffoldBackgroundColor,
        appBar: AppBar(
          title: const Text('Transaction History'),
          elevation: 0,
          backgroundColor: theme.colorScheme.surface,
        ),
        body: const Center(
          child: CircularProgressIndicator(),
        ),
      );
    }

    // Show error state
    if (_error != null) {
      return Scaffold(
        backgroundColor: theme.scaffoldBackgroundColor,
        appBar: AppBar(
          title: const Text('Transaction History'),
          elevation: 0,
          backgroundColor: theme.colorScheme.surface,
        ),
        body: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.error_outline,
                size: 64,
                color: theme.colorScheme.error,
              ),
              const SizedBox(height: 16),
              Text(
                'Failed to load transactions',
                style: theme.textTheme.headlineSmall,
              ),
              const SizedBox(height: 8),
              Text(
                _error!,
                style: theme.textTheme.bodyMedium,
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 24),
              ElevatedButton(
                onPressed: _loadData,
                child: const Text('Retry'),
              ),
            ],
          ),
        ),
      );
    }

    // Show empty state if no cashbooks
    if (_cashbooks.isEmpty) {
      return Scaffold(
        backgroundColor: theme.scaffoldBackgroundColor,
        appBar: AppBar(
          title: const Text('Transaction History'),
          elevation: 0,
          backgroundColor: theme.colorScheme.surface,
        ),
        body: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.account_balance_wallet_outlined,
                size: 64,
                color: theme.colorScheme.primary,
              ),
              const SizedBox(height: 16),
              Text(
                'No Cashbooks Found',
                style: theme.textTheme.headlineSmall,
              ),
              const SizedBox(height: 8),
              Text(
                'Create a cashbook to start tracking transactions',
                style: theme.textTheme.bodyMedium,
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 24),
              ElevatedButton(
                onPressed: () =>
                    Navigator.pushNamed(context, '/cashbook-management'),
                child: const Text('Create Cashbook'),
              ),
            ],
          ),
        ),
      );
    }

    final filteredTransactions = _getFilteredTransactions();

    return Scaffold(
      backgroundColor: theme.scaffoldBackgroundColor,
      appBar: AppBar(
        title: const Text('Transaction History'),
        elevation: 0,
        backgroundColor: theme.colorScheme.surface,
        bottom: PreferredSize(
          preferredSize: const Size.fromHeight(48),
          child: CustomTabBar.transactionHistory(
            currentIndex: _currentTabIndex,
            onTap: (index) {
              _tabController.animateTo(index);
            },
          ),
        ),
      ),
      body: Column(
        children: [
          // Cashbook selector
          if (_cashbooks.isNotEmpty)
            CashbookSelector(
              selectedCashbook: _cashbooks
                  .firstWhere(
                    (cb) => cb.id == _selectedCashbookId,
                    orElse: () => _cashbooks.first,
                  )
                  .name,
              cashbooks: _cashbooksAsMap,
              onCashbookChanged: (cashbookName) {
                final selectedCashbook = _cashbooks.firstWhere(
                  (cb) => cb.name == cashbookName,
                );
                setState(() {
                  _selectedCashbookId = selectedCashbook.id;
                  _clearSelection();
                });
                _loadTransactionsForCashbook(selectedCashbook.id);
              },
            ),

          // Search bar
          TransactionSearchBar(
            searchQuery: _searchQuery,
            onSearchChanged: (query) {
              setState(() {
                _searchQuery = query;
                _clearSelection();
              });
            },
            onFilterPressed: _showFilterBottomSheet,
            activeFiltersCount: _getActiveFiltersCount(),
          ),

          // Filter chips
          FilterChips(
            activeFilters: _activeFilters,
            onClearAll: () {
              setState(() {
                _activeFilters.clear();
                _clearSelection();
              });
            },
            onRemoveFilter: (filterKey) {
              setState(() {
                _activeFilters.remove(filterKey);
                _clearSelection();
              });
            },
          ),

          // Transaction list
          Expanded(
            child: TransactionList(
              cashbook: _cashbooks.firstWhere(
                (cb) => cb.id == _selectedCashbookId,
                orElse: () => _cashbooks.first,
              ),
              transactions: filteredTransactions,
              searchQuery: _searchQuery,
              isMultiSelectMode: _isMultiSelectMode,
              selectedTransactions: _selectedTransactions
                  .map((id) =>
                      filteredTransactions.indexWhere((txn) => txn['id'] == id))
                  .where((index) => index != -1)
                  .toList(),
              onSelectionChanged: (selectedIndices) {
                setState(() {
                  _selectedTransactions = selectedIndices
                      .map((index) => filteredTransactions
                          .where((txn) => txn['id'] == index)
                          .first['id'] as String)
                      .toList();
                  _isMultiSelectMode = _selectedTransactions.isNotEmpty;
                });
              },
              onEditTransaction: _editTransaction,
              onDuplicateTransaction: _duplicateTransaction,
              onDeleteTransaction: _deleteTransaction,
              onRefresh: _refreshTransactions,
              onLoadMore: _loadMoreTransactions,
              isLoading: _isLoading,
              hasMoreData: _hasMoreData,
            ),
          ),
        ],
      ),
      bottomNavigationBar: _isMultiSelectMode
          ? BulkActionsBar(
              selectedCount: _selectedTransactions.length,
              onClearSelection: _clearSelection,
              onBulkDelete: _bulkDeleteTransactions,
              onBulkExport: _bulkExportTransactions,
              onBulkCategorize: _bulkCategorizeTransactions,
            )
          : null,
    );
  }
}
