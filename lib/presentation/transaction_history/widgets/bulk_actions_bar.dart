import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';

import '../../../core/app_export.dart';

class BulkActionsBar extends StatelessWidget {
  final int selectedCount;
  final VoidCallback onClearSelection;
  final VoidCallback onBulkDelete;
  final VoidCallback onBulkExport;
  final VoidCallback onBulkCategorize;

  const BulkActionsBar({
    super.key,
    required this.selectedCount,
    required this.onClearSelection,
    required this.onBulkDelete,
    required this.onBulkExport,
    required this.onBulkCategorize,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    return Container(
      padding: EdgeInsets.symmetric(horizontal: 4.w, vertical: 2.h),
      decoration: BoxDecoration(
        color: theme.colorScheme.primary,
        boxShadow: [
          BoxShadow(
            color: isDark
                ? Colors.white.withValues(alpha: 0.1)
                : Colors.black.withValues(alpha: 0.1),
            offset: const Offset(0, -2),
            blurRadius: 8,
            spreadRadius: 0,
          ),
        ],
      ),
      child: SafeArea(
        top: false,
        child: Row(
          children: [
            // Close button
            GestureDetector(
              onTap: onClearSelection,
              child: Container(
                padding: EdgeInsets.all(2.w),
                child: CustomIconWidget(
                  iconName: 'close',
                  color: Colors.white,
                  size: 24,
                ),
              ),
            ),

            SizedBox(width: 4.w),

            // Selected count
            Expanded(
              child: Text(
                '$selectedCount selected',
                style: theme.textTheme.titleMedium?.copyWith(
                  fontSize: 16.sp,
                  fontWeight: FontWeight.w600,
                  color: Colors.white,
                ),
              ),
            ),

            // Action buttons
            Row(
              children: [
                // Categorize button
                GestureDetector(
                  onTap: onBulkCategorize,
                  child: Container(
                    padding: EdgeInsets.all(2.w),
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        CustomIconWidget(
                          iconName: 'category',
                          color: Colors.white,
                          size: 24,
                        ),
                        SizedBox(height: 0.5.h),
                        Text(
                          'Category',
                          style: theme.textTheme.bodySmall?.copyWith(
                            fontSize: 10.sp,
                            color: Colors.white,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),

                SizedBox(width: 4.w),

                // Export button
                GestureDetector(
                  onTap: onBulkExport,
                  child: Container(
                    padding: EdgeInsets.all(2.w),
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        CustomIconWidget(
                          iconName: 'download',
                          color: Colors.white,
                          size: 24,
                        ),
                        SizedBox(height: 0.5.h),
                        Text(
                          'Export',
                          style: theme.textTheme.bodySmall?.copyWith(
                            fontSize: 10.sp,
                            color: Colors.white,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),

                SizedBox(width: 4.w),

                // Delete button
                GestureDetector(
                  onTap: () => _showBulkDeleteConfirmation(context),
                  child: Container(
                    padding: EdgeInsets.all(2.w),
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        CustomIconWidget(
                          iconName: 'delete',
                          color: Colors.white,
                          size: 24,
                        ),
                        SizedBox(height: 0.5.h),
                        Text(
                          'Delete',
                          style: theme.textTheme.bodySmall?.copyWith(
                            fontSize: 10.sp,
                            color: Colors.white,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  void _showBulkDeleteConfirmation(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Transactions'),
        content: Text(
          'Are you sure you want to delete $selectedCount selected transactions? This action cannot be undone.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              onBulkDelete();
            },
            style: TextButton.styleFrom(
              foregroundColor: AppTheme.getErrorColor(
                Theme.of(context).brightness == Brightness.dark,
              ),
            ),
            child: const Text('Delete'),
          ),
        ],
      ),
    );
  }
}
