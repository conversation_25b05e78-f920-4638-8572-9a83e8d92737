import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';

import '../../../core/app_export.dart';

class TransactionCard extends StatefulWidget {
  final Map<String, dynamic> transaction;
  final String searchQuery;
  final bool isSelected;
  final VoidCallback? onTap;
  final VoidCallback? onEdit;
  final VoidCallback? onDuplicate;
  final VoidCallback? onDelete;
  final ValueChanged<bool>? onSelectionChanged;
  final CashbookModel cashbook;

  const TransactionCard({
    super.key,
    required this.transaction,
    required this.cashbook,
    this.searchQuery = '',
    this.isSelected = false,
    this.onTap,
    this.onEdit,
    this.onDuplicate,
    this.onDelete,
    this.onSelectionChanged,
  });

  @override
  State<TransactionCard> createState() => _TransactionCardState();
}

class _TransactionCardState extends State<TransactionCard>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<Offset> _slideAnimation;
  bool _isSwipeRevealed = false;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _slideAnimation = Tween<Offset>(
      begin: Offset.zero,
      end: const Offset(-0.3, 0),
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;
    final amount = widget.transaction['amount'] as double;
    final isIncome = widget.transaction['type'] == 'income';
    final description = widget.transaction['description'];
    final category = widget.transaction['category'];
    final paymentMode = widget.transaction['paymentMode'];
    final hasAttachment = widget.transaction['hasAttachment'] as bool? ?? false;
    final date = widget.transaction['date'] as DateTime;

    return GestureDetector(
      onTap: widget.onTap,
      onLongPress: () {
        if (widget.onSelectionChanged != null) {
          widget.onSelectionChanged!(!widget.isSelected);
        }
      },
      child: Stack(
        children: [
          // Swipe actions background
          if (_isSwipeRevealed) _buildSwipeActions(theme),

          // Main card content
          SlideTransition(
            position: _slideAnimation,
            child: GestureDetector(
              onPanUpdate: (details) {
                if (details.delta.dx < -5 && !_isSwipeRevealed) {
                  setState(() => _isSwipeRevealed = true);
                  _animationController.forward();
                } else if (details.delta.dx > 5 && _isSwipeRevealed) {
                  setState(() => _isSwipeRevealed = false);
                  _animationController.reverse();
                }
              },
              child: Container(
                margin: EdgeInsets.symmetric(horizontal: 4.w, vertical: 0.5.h),
                padding: EdgeInsets.all(4.w),
                decoration: BoxDecoration(
                  color: widget.isSelected
                      ? theme.colorScheme.primary.withValues(alpha: 0.1)
                      : theme.colorScheme.surface,
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(
                    color: widget.isSelected
                        ? theme.colorScheme.primary
                        : (isDark
                            ? const Color(0xFF424242)
                            : const Color(0xFFE0E0E0)),
                    width: widget.isSelected ? 2 : 1,
                  ),
                  boxShadow: [
                    BoxShadow(
                      color: isDark
                          ? Colors.white.withValues(alpha: 0.02)
                          : Colors.black.withValues(alpha: 0.04),
                      offset: const Offset(0, 2),
                      blurRadius: 4,
                      spreadRadius: 0,
                    ),
                  ],
                ),
                child: Row(
                  children: [
                    // Category icon
                    Container(
                      width: 12.w,
                      height: 6.h,
                      decoration: BoxDecoration(
                        color:
                            _getCategoryColor(category).withValues(alpha: 0.2),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: CustomIconWidget(
                        iconName: _getCategoryIcon(category),
                        color: _getCategoryColor(category),
                        size: 24,
                      ),
                    ),
                    SizedBox(width: 4.w),

                    // Transaction details
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              Expanded(
                                child: _buildHighlightedText(
                                  description,
                                  widget.searchQuery,
                                  theme.textTheme.bodyLarge?.copyWith(
                                        fontSize: 12.sp,
                                        fontWeight: FontWeight.w600,
                                      ) ??
                                      const TextStyle(),
                                  theme.colorScheme.primary,
                                ),
                              ),
                              if (hasAttachment) ...[
                                SizedBox(width: 2.w),
                                CustomIconWidget(
                                  iconName: 'attach_file',
                                  color: theme.colorScheme.onSurfaceVariant,
                                  size: 16,
                                ),
                              ],
                            ],
                          ),
                          SizedBox(height: 0.5.h),
                          Container(
                            constraints: BoxConstraints(
                              maxWidth: MediaQuery.of(context).size.width * 0.6,
                            ),
                            child: Wrap(
                              spacing: 2, // space between items
                              runSpacing:
                                  2, // space between lines when wrapping
                              children: [
                                Text(
                                  category,
                                  style: theme.textTheme.bodySmall?.copyWith(
                                    fontSize: 8.sp,
                                    color: theme.colorScheme.onSurfaceVariant,
                                  ),
                                ),
                                Text(
                                  '•',
                                  style: theme.textTheme.bodySmall?.copyWith(
                                    fontSize: 8.sp,
                                    color: theme.colorScheme.onSurfaceVariant,
                                  ),
                                ),
                                Text(
                                  paymentMode,
                                  style: theme.textTheme.bodySmall?.copyWith(
                                    fontSize: 8.sp,
                                    color: theme.colorScheme.onSurfaceVariant,
                                  ),
                                ),
                              ],
                            ),
                          ),
                          SizedBox(height: 0.5.h),
                          Text(
                            _formatDate(date),
                            style: theme.textTheme.bodySmall?.copyWith(
                              fontSize: 10.sp,
                              color: theme.colorScheme.onSurfaceVariant,
                            ),
                          ),
                        ],
                      ),
                    ),

                    // Amount
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.end,
                      children: [
                        Text(
                          '${isIncome ? '+' : '-'} ${widget.cashbook.currency} ${amount.toStringAsFixed(2)}',
                          style: theme.textTheme.bodyLarge?.copyWith(
                            fontSize: 12.sp,
                            fontWeight: FontWeight.w700,
                            color: isIncome
                                ? AppTheme.getSuccessColor(isDark)
                                : AppTheme.getErrorColor(isDark),
                          ),
                        ),
                        if (widget.isSelected)
                          Container(
                            margin: EdgeInsets.only(top: 1.h),
                            child: CustomIconWidget(
                              iconName: 'check_circle',
                              color: theme.colorScheme.primary,
                              size: 20,
                            ),
                          ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSwipeActions(ThemeData theme) {
    return Positioned.fill(
      child: Container(
        margin: EdgeInsets.symmetric(horizontal: 4.w, vertical: 0.5.h),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.end,
          children: [
            // Edit action
            GestureDetector(
              onTap: () {
                _resetSwipe();
                widget.onEdit?.call();
              },
              child: Container(
                width: 15.w,
                decoration: BoxDecoration(
                  color: theme.colorScheme.primary,
                  borderRadius: const BorderRadius.only(
                    topLeft: Radius.circular(12),
                    bottomLeft: Radius.circular(12),
                  ),
                ),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    CustomIconWidget(
                      iconName: 'edit',
                      color: Colors.white,
                      size: 20,
                    ),
                    SizedBox(height: 0.5.h),
                    Text(
                      'Edit',
                      style: theme.textTheme.bodySmall?.copyWith(
                        color: Colors.white,
                        fontSize: 10.sp,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
              ),
            ),

            // Duplicate action
            // GestureDetector(
            //   onTap: () {
            //     _resetSwipe();
            //     widget.onDuplicate?.call();
            //   },
            //   child: Container(
            //     width: 15.w,
            //     color: AppTheme.getAccentColor(
            //         theme.brightness == Brightness.dark),
            //     child: Column(
            //       mainAxisAlignment: MainAxisAlignment.center,
            //       children: [
            //         CustomIconWidget(
            //           iconName: 'content_copy',
            //           color: Colors.white,
            //           size: 20,
            //         ),
            //         SizedBox(height: 0.5.h),
            //         Text(
            //           'Copy',
            //           style: theme.textTheme.bodySmall?.copyWith(
            //             color: Colors.white,
            //             fontSize: 10.sp,
            //             fontWeight: FontWeight.w500,
            //           ),
            //         ),
            //       ],
            //     ),
            //   ),
            // ),

            // Delete action
            GestureDetector(
              onTap: () {
                _resetSwipe();
                _showDeleteConfirmation();
              },
              child: Container(
                width: 15.w,
                decoration: BoxDecoration(
                  color: AppTheme.getErrorColor(
                      theme.brightness == Brightness.dark),
                  borderRadius: const BorderRadius.only(
                    topRight: Radius.circular(12),
                    bottomRight: Radius.circular(12),
                  ),
                ),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    CustomIconWidget(
                      iconName: 'delete',
                      color: Colors.white,
                      size: 20,
                    ),
                    SizedBox(height: 0.5.h),
                    Text(
                      'Delete',
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                            color: Colors.white,
                            fontSize: 10.sp,
                            fontWeight: FontWeight.w500,
                          ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildHighlightedText(
    String text,
    String query,
    TextStyle style,
    Color highlightColor,
  ) {
    if (query.isEmpty) {
      return Text(text, style: style, overflow: TextOverflow.ellipsis);
    }

    final lowerText = text.toLowerCase();
    final lowerQuery = query.toLowerCase();
    final index = lowerText.indexOf(lowerQuery);

    if (index == -1) {
      return Text(text, style: style, overflow: TextOverflow.ellipsis);
    }

    return RichText(
      overflow: TextOverflow.ellipsis,
      text: TextSpan(
        children: [
          if (index > 0)
            TextSpan(
              text: text.substring(0, index),
              style: style,
            ),
          TextSpan(
            text: text.substring(index, index + query.length),
            style: style.copyWith(
              backgroundColor: highlightColor.withValues(alpha: 0.3),
              fontWeight: FontWeight.w700,
            ),
          ),
          if (index + query.length < text.length)
            TextSpan(
              text: text.substring(index + query.length),
              style: style,
            ),
        ],
      ),
    );
  }

  void _resetSwipe() {
    setState(() => _isSwipeRevealed = false);
    _animationController.reverse();
  }

  void _showDeleteConfirmation() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Transaction'),
        content: const Text(
            'Are you sure you want to delete this transaction? This action cannot be undone.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              widget.onDelete?.call();
            },
            style: TextButton.styleFrom(
              foregroundColor: AppTheme.getErrorColor(
                  Theme.of(context).brightness == Brightness.dark),
            ),
            child: const Text('Delete'),
          ),
        ],
      ),
    );
  }

  Color _getCategoryColor(String category) {
    switch (category.toLowerCase()) {
      case 'food':
        return const Color(0xFFFF6B6B);
      case 'transport':
        return const Color(0xFF4ECDC4);
      case 'shopping':
        return const Color(0xFFFFE66D);
      case 'entertainment':
        return const Color(0xFFFF8B94);
      case 'bills':
        return const Color(0xFFA8E6CF);
      case 'salary':
        return const Color(0xFF88D8B0);
      case 'business':
        return const Color(0xFFB4A7D6);
      default:
        return const Color(0xFF95A5A6);
    }
  }

  String _getCategoryIcon(String category) {
    switch (category.toLowerCase()) {
      case 'food':
        return 'restaurant';
      case 'transport':
        return 'directions_car';
      case 'shopping':
        return 'shopping_bag';
      case 'entertainment':
        return 'movie';
      case 'bills':
        return 'receipt';
      case 'salary':
        return 'account_balance_wallet';
      case 'business':
        return 'business';
      default:
        return 'category';
    }
  }

  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date).inDays;

    if (difference == 0) {
      return 'Today';
    } else if (difference == 1) {
      return 'Yesterday';
    } else if (difference < 7) {
      return '${difference} days ago';
    } else {
      return '${date.month}/${date.day}/${date.year}';
    }
  }
}
