import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';

import '../../../core/app_export.dart';
import '../../../widgets/custom_icon_widget.dart';
import './transaction_card.dart';

class TransactionList extends StatefulWidget {
  final List<Map<String, dynamic>> transactions;
  final String searchQuery;
  final bool isMultiSelectMode;
  final List<int> selectedTransactions;
  final ValueChanged<List<String>> onSelectionChanged;
  final Function(Map<String, dynamic>) onEditTransaction;
  final Function(Map<String, dynamic>) onDuplicateTransaction;
  final Function(Map<String, dynamic>) onDeleteTransaction;
  final VoidCallback onRefresh;
  final VoidCallback onLoadMore;
  final bool isLoading;
  final bool hasMoreData;
  final CashbookModel cashbook;

  const TransactionList({
    super.key,
    required this.transactions,
    required this.cashbook,
    this.searchQuery = '',
    this.isMultiSelectMode = false,
    this.selectedTransactions = const [],
    required this.onSelectionChanged,
    required this.onEditTransaction,
    required this.onDuplicateTransaction,
    required this.onDeleteTransaction,
    required this.onRefresh,
    required this.onLoadMore,
    this.isLoading = false,
    this.hasMoreData = true,
  });

  @override
  State<TransactionList> createState() => _TransactionListState();
}

class _TransactionListState extends State<TransactionList> {
  final ScrollController _scrollController = ScrollController();
  bool _isRefreshing = false;

  @override
  void initState() {
    super.initState();
    _scrollController.addListener(_onScroll);
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  void _onScroll() {
    if (_scrollController.position.pixels >=
        _scrollController.position.maxScrollExtent - 200) {
      if (widget.hasMoreData && !widget.isLoading) {
        widget.onLoadMore();
      }
    }
  }

  Future<void> _onRefresh() async {
    setState(() => _isRefreshing = true);
    widget.onRefresh();
    await Future.delayed(const Duration(milliseconds: 500));
    setState(() => _isRefreshing = false);
  }

  @override
  Widget build(BuildContext context) {
    if (widget.transactions.isEmpty && !widget.isLoading) {
      return _buildEmptyState(context);
    }

    final groupedTransactions = _groupTransactionsByDate(widget.transactions);

    return RefreshIndicator(
      onRefresh: _onRefresh,
      child: CustomScrollView(
        controller: _scrollController,
        physics: const AlwaysScrollableScrollPhysics(),
        slivers: [
          ...groupedTransactions.entries
              .map((entry) => [
                    _buildDateHeader(context, entry.key),
                    SliverList(
                      delegate: SliverChildBuilderDelegate(
                        (context, index) {
                          final transaction = entry.value[index];
                          final transactionId = transaction['id'] as String;
                          final isSelected = widget.selectedTransactions
                              .contains(transactionId);

                          return TransactionCard(
                            cashbook: widget.cashbook,
                            transaction: transaction,
                            searchQuery: widget.searchQuery,
                            isSelected: isSelected,
                            onTap: widget.isMultiSelectMode
                                ? () => _toggleSelection(transaction['id'])
                                : null,
                            onEdit: () => widget.onEditTransaction(transaction),
                            onDuplicate: () =>
                                widget.onDuplicateTransaction(transaction),
                            onDelete: () =>
                                widget.onDeleteTransaction(transaction),
                            onSelectionChanged: widget.isMultiSelectMode
                                ? (selected) => _toggleSelection(transactionId)
                                : null,
                          );
                        },
                        childCount: entry.value.length,
                      ),
                    ),
                  ])
              .expand((x) => x)
              .toList(),

          // Loading indicator
          if (widget.isLoading)
            SliverToBoxAdapter(
              child: Container(
                padding: EdgeInsets.all(4.w),
                child: const Center(
                  child: CircularProgressIndicator(),
                ),
              ),
            ),

          // End of list indicator
          if (!widget.hasMoreData && widget.transactions.isNotEmpty)
            SliverToBoxAdapter(
              child: Container(
                padding: EdgeInsets.all(4.w),
                child: Center(
                  child: Text(
                    'No more transactions',
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          fontSize: 12.sp,
                          color: Theme.of(context).colorScheme.onSurfaceVariant,
                        ),
                  ),
                ),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildDateHeader(BuildContext context, String date) {
    final theme = Theme.of(context);

    return SliverToBoxAdapter(
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: 4.w, vertical: 2.h),
        child: Row(
          children: [
            Container(
              padding: EdgeInsets.symmetric(horizontal: 3.w, vertical: 1.h),
              decoration: BoxDecoration(
                color: theme.colorScheme.primary.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(20),
              ),
              child: Text(
                date,
                style: theme.textTheme.bodySmall?.copyWith(
                  fontSize: 10.sp,
                  fontWeight: FontWeight.w600,
                  color: theme.colorScheme.primary,
                ),
              ),
            ),
            Expanded(
              child: Container(
                margin: EdgeInsets.only(left: 3.w),
                height: 1,
                color: theme.colorScheme.outline.withValues(alpha: 0.3),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildEmptyState(BuildContext context) {
    final theme = Theme.of(context);
    final hasSearchQuery = widget.searchQuery.isNotEmpty;

    return Center(
      child: Padding(
        padding: EdgeInsets.all(8.w),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              width: 30.w,
              height: 15.h,
              decoration: BoxDecoration(
                color: theme.colorScheme.primary.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(20),
              ),
              child: CustomIconWidget(
                iconName: hasSearchQuery ? 'search_off' : 'receipt_long',
                color: theme.colorScheme.primary,
                size: 48,
              ),
            ),
            SizedBox(height: 4.h),
            Text(
              hasSearchQuery ? 'No transactions found' : 'No transactions yet',
              style: theme.textTheme.titleLarge?.copyWith(
                fontSize: 20.sp,
                fontWeight: FontWeight.w600,
              ),
            ),
            SizedBox(height: 2.h),
            Text(
              hasSearchQuery
                  ? 'Try adjusting your search or filters to find what you\'re looking for.'
                  : 'Start tracking your finances by adding your first transaction.',
              style: theme.textTheme.bodyMedium?.copyWith(
                fontSize: 14.sp,
                color: theme.colorScheme.onSurfaceVariant,
              ),
              textAlign: TextAlign.center,
            ),
            SizedBox(height: 4.h),
            if (hasSearchQuery)
              OutlinedButton(
                onPressed: () {
                  // Clear search and filters
                },
                child: const Text('Clear Filters'),
              )
            else
              ElevatedButton(
                onPressed: () {
                  Navigator.pushNamed(context, '/add-transaction');
                },
                child: const Text('Add First Transaction'),
              ),
          ],
        ),
      ),
    );
  }

  Map<String, List<Map<String, dynamic>>> _groupTransactionsByDate(
    List<Map<String, dynamic>> transactions,
  ) {
    final grouped = <String, List<Map<String, dynamic>>>{};

    for (final transaction in transactions) {
      final date = transaction['date'] as DateTime;
      final dateKey = _getDateKey(date);

      if (!grouped.containsKey(dateKey)) {
        grouped[dateKey] = [];
      }
      grouped[dateKey]!.add(transaction);
    }

    return grouped;
  }

  String _getDateKey(DateTime date) {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final yesterday = today.subtract(const Duration(days: 1));
    final transactionDate = DateTime(date.year, date.month, date.day);

    if (transactionDate == today) {
      return 'Today';
    } else if (transactionDate == yesterday) {
      return 'Yesterday';
    } else if (now.difference(transactionDate).inDays < 7) {
      final weekdays = [
        'Monday',
        'Tuesday',
        'Wednesday',
        'Thursday',
        'Friday',
        'Saturday',
        'Sunday'
      ];
      return weekdays[date.weekday - 1];
    } else {
      return '${date.month}/${date.day}/${date.year}';
    }
  }

  void _toggleSelection(String transactionId) {
    final currentSelection = List<String>.from(widget.selectedTransactions);

    if (currentSelection.contains(transactionId)) {
      currentSelection.remove(transactionId);
    } else {
      currentSelection.add(transactionId);
    }

    widget.onSelectionChanged(currentSelection);
  }
}
