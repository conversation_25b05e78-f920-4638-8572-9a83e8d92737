import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';

import '../../../core/app_export.dart';
import '../../../widgets/custom_icon_widget.dart';

class FilterChips extends StatelessWidget {
  final Map<String, dynamic> activeFilters;
  final VoidCallback onClearAll;
  final Function(String) onRemoveFilter;

  const FilterChips({
    super.key,
    required this.activeFilters,
    required this.onClearAll,
    required this.onRemoveFilter,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final filterChips = _buildFilterChips();

    if (filterChips.isEmpty) {
      return const SizedBox.shrink();
    }

    return Container(
      padding: EdgeInsets.symmetric(horizontal: 4.w, vertical: 1.h),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Active Filters (${filterChips.length})',
                style: theme.textTheme.bodySmall?.copyWith(
                  fontSize: 12.sp,
                  fontWeight: FontWeight.w500,
                  color: theme.colorScheme.onSurfaceVariant,
                ),
              ),
              GestureDetector(
                onTap: onClearAll,
                child: Text(
                  'Clear All',
                  style: theme.textTheme.bodySmall?.copyWith(
                    fontSize: 12.sp,
                    fontWeight: FontWeight.w500,
                    color: theme.colorScheme.primary,
                  ),
                ),
              ),
            ],
          ),
          SizedBox(height: 1.h),
          Wrap(
            spacing: 2.w,
            runSpacing: 1.h,
            children: filterChips,
          ),
        ],
      ),
    );
  }

  List<Widget> _buildFilterChips() {
    List<Widget> chips = [];

    // Date range filter
    if (activeFilters['dateRange'] != null) {
      final dateRange = activeFilters['dateRange'] as Map<String, DateTime>;
      final startDate = dateRange['start']!;
      final endDate = dateRange['end']!;

      chips.add(_buildFilterChip(
        'dateRange',
        '${_formatDate(startDate)} - ${_formatDate(endDate)}',
        Icons.date_range,
      ));
    }

    // Categories filter
    if (activeFilters['categories'] != null &&
        (activeFilters['categories'] as List).isNotEmpty) {
      final categories = activeFilters['categories'] as List<String>;
      chips.add(_buildFilterChip(
        'categories',
        categories.length == 1
            ? categories.first
            : '${categories.length} categories',
        Icons.category,
      ));
    }

    // Payment modes filter
    if (activeFilters['paymentModes'] != null &&
        (activeFilters['paymentModes'] as List).isNotEmpty) {
      final paymentModes = activeFilters['paymentModes'] as List<String>;
      chips.add(_buildFilterChip(
        'paymentModes',
        paymentModes.length == 1
            ? paymentModes.first
            : '${paymentModes.length} payment modes',
        Icons.payment,
      ));
    }

    // Amount range filter
    if (activeFilters['amountRange'] != null) {
      final amountRange = activeFilters['amountRange'] as Map<String, double>;
      final minAmount = amountRange['min']!;
      final maxAmount = amountRange['max']!;

      chips.add(_buildFilterChip(
        'amountRange',
        '\$${minAmount.toStringAsFixed(0)} - \$${maxAmount.toStringAsFixed(0)}',
        Icons.attach_money,
      ));
    }

    return chips;
  }

  Widget _buildFilterChip(String filterKey, String label, IconData icon) {
    return Builder(
      builder: (context) {
        final theme = Theme.of(context);

        return Container(
          padding: EdgeInsets.symmetric(horizontal: 3.w, vertical: 0.5.h),
          decoration: BoxDecoration(
            color: theme.colorScheme.primary.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(20),
            border: Border.all(
              color: theme.colorScheme.primary.withValues(alpha: 0.3),
              width: 1,
            ),
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              CustomIconWidget(
                iconName: _getIconName(icon),
                color: theme.colorScheme.primary,
                size: 16,
              ),
              SizedBox(width: 2.w),
              Flexible(
                child: Text(
                  label,
                  style: theme.textTheme.bodySmall?.copyWith(
                    fontSize: 12.sp,
                    fontWeight: FontWeight.w500,
                    color: theme.colorScheme.primary,
                  ),
                  overflow: TextOverflow.ellipsis,
                ),
              ),
              SizedBox(width: 2.w),
              GestureDetector(
                onTap: () => onRemoveFilter(filterKey),
                child: CustomIconWidget(
                  iconName: 'close',
                  color: theme.colorScheme.primary,
                  size: 16,
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  String _getIconName(IconData icon) {
    switch (icon) {
      case Icons.date_range:
        return 'date_range';
      case Icons.category:
        return 'category';
      case Icons.payment:
        return 'payment';
      case Icons.attach_money:
        return 'attach_money';
      default:
        return 'filter_alt';
    }
  }

  String _formatDate(DateTime date) {
    return '${date.month}/${date.day}/${date.year}';
  }
}
