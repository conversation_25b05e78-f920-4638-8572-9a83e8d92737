import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';

import '../../../core/app_export.dart';

/// Context menu widget for category actions (Edit, Delete, Set as Default, View Transactions)
class CategoryContextMenuWidget extends StatelessWidget {
  final Map<String, dynamic> category;
  final VoidCallback onEdit;
  final VoidCallback onDelete;
  final VoidCallback onSetDefault;
  final VoidCallback onViewTransactions;
  final VoidCallback onClose;

  const CategoryContextMenuWidget({
    super.key,
    required this.category,
    required this.onEdit,
    required this.onDelete,
    required this.onSetDefault,
    required this.onViewTransactions,
    required this.onClose,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;
    final isDefault = category['isDefault'] == true;

    return Material(
      color: Colors.transparent,
      child: Container(
        constraints: BoxConstraints(
          maxWidth: 50.w,
          maxHeight: 30.h,
        ),
        decoration: BoxDecoration(
          color: theme.cardColor,
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: isDark
                  ? Colors.white.withValues(alpha: 0.1)
                  : Colors.black.withValues(alpha: 0.15),
              offset: const Offset(0, 8),
              blurRadius: 24,
              spreadRadius: 0,
            ),
          ],
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Header with category info
            Container(
              padding: EdgeInsets.all(4.w),
              decoration: BoxDecoration(
                border: Border(
                  bottom: BorderSide(
                    color: isDark
                        ? const Color(0xFF424242)
                        : const Color(0xFFE0E0E0),
                    width: 1,
                  ),
                ),
              ),
              child: Row(
                children: [
                  Container(
                    width: 8.w,
                    height: 4.h,
                    decoration: BoxDecoration(
                      color: ((category['type'] as String) == 'income'
                              ? AppTheme.getSuccessColor(isDark)
                              : AppTheme.getErrorColor(isDark))
                          .withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(6),
                    ),
                    child: Center(
                      child: CustomIconWidget(
                        iconName: category['icon'] as String,
                        color: (category['type'] as String) == 'income'
                            ? AppTheme.getSuccessColor(isDark)
                            : AppTheme.getErrorColor(isDark),
                        size: 4.w,
                      ),
                    ),
                  ),
                  SizedBox(width: 3.w),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          category['name'] as String,
                          style: theme.textTheme.titleSmall?.copyWith(
                            fontWeight: FontWeight.w600,
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                        Text(
                          '${category['transactionCount']} transactions',
                          style: theme.textTheme.bodySmall?.copyWith(
                            color: isDark
                                ? const Color(0xFFB0B0B0)
                                : const Color(0xFF757575),
                          ),
                        ),
                      ],
                    ),
                  ),
                  GestureDetector(
                    onTap: onClose,
                    child: CustomIconWidget(
                      iconName: 'close',
                      color: isDark
                          ? const Color(0xFFB0B0B0)
                          : const Color(0xFF757575),
                      size: 5.w,
                    ),
                  ),
                ],
              ),
            ),

            // Menu Options
            Flexible(
              child: SingleChildScrollView(
                child: Column(
                  children: [
                    _buildMenuItem(
                      context,
                      icon: 'edit',
                      title: 'Edit Category',
                      onTap: () {
                        onClose();
                        onEdit();
                      },
                    ),
                    if (!isDefault)
                      _buildMenuItem(
                        context,
                        icon: 'star_outline',
                        title: 'Set as Default',
                        onTap: () {
                          onClose();
                          onSetDefault();
                        },
                      ),
                    _buildMenuItem(
                      context,
                      icon: 'history',
                      title: 'View Transactions',
                      onTap: () {
                        onClose();
                        onViewTransactions();
                      },
                    ),
                    if (!isDefault)
                      _buildMenuItem(
                        context,
                        icon: 'delete_outline',
                        title: 'Delete Category',
                        isDestructive: true,
                        onTap: () {
                          onClose();
                          _showDeleteConfirmation(context);
                        },
                      ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildMenuItem(
    BuildContext context, {
    required String icon,
    required String title,
    required VoidCallback onTap,
    bool isDestructive = false,
  }) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    return InkWell(
      onTap: onTap,
      child: Container(
        width: double.infinity,
        padding: EdgeInsets.symmetric(horizontal: 4.w, vertical: 3.h),
        child: Row(
          children: [
            CustomIconWidget(
              iconName: icon,
              color: isDestructive
                  ? AppTheme.getErrorColor(isDark)
                  : (isDark
                      ? const Color(0xFFB0B0B0)
                      : const Color(0xFF757575)),
              size: 5.w,
            ),
            SizedBox(width: 3.w),
            Expanded(
              child: Text(
                title,
                style: theme.textTheme.bodyMedium?.copyWith(
                  color: isDestructive
                      ? AppTheme.getErrorColor(isDark)
                      : (isDark ? Colors.white : const Color(0xFF212121)),
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _showDeleteConfirmation(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          backgroundColor: theme.cardColor,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          title: Row(
            children: [
              CustomIconWidget(
                iconName: 'warning',
                color: AppTheme.getWarningColor(isDark),
                size: 6.w,
              ),
              SizedBox(width: 3.w),
              Expanded(
                child: Text(
                  'Delete Category',
                  style: theme.textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ],
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Are you sure you want to delete "${category['name']}"?',
                style: theme.textTheme.bodyMedium,
              ),
              SizedBox(height: 2.h),
              if ((category['transactionCount'] as int) > 0)
                Container(
                  padding: EdgeInsets.all(3.w),
                  decoration: BoxDecoration(
                    color:
                        AppTheme.getWarningColor(isDark).withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Row(
                    children: [
                      CustomIconWidget(
                        iconName: 'info',
                        color: AppTheme.getWarningColor(isDark),
                        size: 4.w,
                      ),
                      SizedBox(width: 2.w),
                      Expanded(
                        child: Text(
                          'This category has ${category['transactionCount']} transactions. They will be moved to "Other".',
                          style: theme.textTheme.bodySmall?.copyWith(
                            color: AppTheme.getWarningColor(isDark),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: Text(
                'Cancel',
                style: theme.textTheme.labelLarge?.copyWith(
                  color: isDark
                      ? const Color(0xFFB0B0B0)
                      : const Color(0xFF757575),
                ),
              ),
            ),
            ElevatedButton(
              onPressed: () {
                Navigator.of(context).pop();
                onDelete();
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: AppTheme.getErrorColor(isDark),
                foregroundColor: Colors.white,
              ),
              child: Text(
                'Delete',
                style: theme.textTheme.labelLarge?.copyWith(
                  color: Colors.white,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ],
        );
      },
    );
  }
}
