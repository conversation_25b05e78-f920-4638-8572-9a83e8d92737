import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';

import '../../../core/app_export.dart';

/// Search widget for filtering categories by name
class CategorySearchWidget extends StatefulWidget {
  final Function(String) onSearchChanged;
  final String currentQuery;

  const CategorySearchWidget({
    super.key,
    required this.onSearchChanged,
    this.currentQuery = '',
  });

  @override
  State<CategorySearchWidget> createState() => _CategorySearchWidgetState();
}

class _CategorySearchWidgetState extends State<CategorySearchWidget> {
  late TextEditingController _searchController;
  bool _isSearchActive = false;

  @override
  void initState() {
    super.initState();
    _searchController = TextEditingController(text: widget.currentQuery);
    _isSearchActive = widget.currentQuery.isNotEmpty;
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    return Container(
      margin: EdgeInsets.symmetric(horizontal: 4.w, vertical: 1.h),
      decoration: BoxDecoration(
        color: theme.cardColor,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: _isSearchActive
              ? AppTheme.getAccentColor(isDark)
              : (isDark ? const Color(0xFF424242) : const Color(0xFFE0E0E0)),
          width: _isSearchActive ? 2 : 1,
        ),
        boxShadow: _isSearchActive
            ? [
                BoxShadow(
                  color: AppTheme.getAccentColor(isDark).withValues(alpha: 0.1),
                  offset: const Offset(0, 2),
                  blurRadius: 8,
                  spreadRadius: 0,
                ),
              ]
            : [
                BoxShadow(
                  color: isDark
                      ? Colors.white.withValues(alpha: 0.02)
                      : Colors.black.withValues(alpha: 0.04),
                  offset: const Offset(0, 1),
                  blurRadius: 2,
                  spreadRadius: 0,
                ),
              ],
      ),
      child: TextField(
        controller: _searchController,
        onChanged: (value) {
          widget.onSearchChanged(value);
          setState(() {
            _isSearchActive = value.isNotEmpty;
          });
        },
        decoration: InputDecoration(
          hintText: 'Search categories...',
          hintStyle: theme.textTheme.bodyMedium?.copyWith(
            color: isDark ? const Color(0xFFB0B0B0) : const Color(0xFF757575),
          ),
          prefixIcon: Padding(
            padding: EdgeInsets.all(3.w),
            child: CustomIconWidget(
              iconName: 'search',
              color: _isSearchActive
                  ? AppTheme.getAccentColor(isDark)
                  : (isDark
                      ? const Color(0xFFB0B0B0)
                      : const Color(0xFF757575)),
              size: 5.w,
            ),
          ),
          suffixIcon: _isSearchActive
              ? GestureDetector(
                  onTap: _clearSearch,
                  child: Padding(
                    padding: EdgeInsets.all(3.w),
                    child: CustomIconWidget(
                      iconName: 'clear',
                      color: isDark
                          ? const Color(0xFFB0B0B0)
                          : const Color(0xFF757575),
                      size: 5.w,
                    ),
                  ),
                )
              : null,
          border: InputBorder.none,
          enabledBorder: InputBorder.none,
          focusedBorder: InputBorder.none,
          contentPadding: EdgeInsets.symmetric(
            horizontal: 4.w,
            vertical: 2.h,
          ),
        ),
        style: theme.textTheme.bodyMedium?.copyWith(
          color: isDark ? Colors.white : const Color(0xFF212121),
        ),
      ),
    );
  }

  void _clearSearch() {
    _searchController.clear();
    widget.onSearchChanged('');
    setState(() {
      _isSearchActive = false;
    });
  }
}
