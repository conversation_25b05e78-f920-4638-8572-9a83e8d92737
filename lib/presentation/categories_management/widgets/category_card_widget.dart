import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';

import '../../../core/app_export.dart';

/// Widget for displaying individual category cards with icon, name, and usage statistics
class CategoryCardWidget extends StatelessWidget {
  final Map<String, dynamic> category;
  final VoidCallback? onTap;
  final VoidCallback? onLongPress;
  final bool isSelected;

  const CategoryCardWidget({
    super.key,
    required this.category,
    this.onTap,
    this.onLongPress,
    this.isSelected = false,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;
    final isIncome = (category['type'] as String) == 'income';

    return GestureDetector(
      onTap: onTap,
      onLongPress: onLongPress,
      child: Container(
        constraints: BoxConstraints(
          minHeight: 12.h,
          maxHeight: 16.h,
        ),
        decoration: BoxDecoration(
          color: isSelected
              ? (isIncome
                  ? AppTheme.getSuccessColor(isDark).withValues(alpha: 0.1)
                  : AppTheme.getErrorColor(isDark).withValues(alpha: 0.1))
              : theme.cardColor,
          borderRadius: BorderRadius.circular(12),
          border: isSelected
              ? Border.all(
                  color: isIncome
                      ? AppTheme.getSuccessColor(isDark)
                      : AppTheme.getErrorColor(isDark),
                  width: 2,
                )
              : Border.all(
                  color: isDark
                      ? const Color(0xFF424242)
                      : const Color(0xFFE0E0E0),
                  width: 1,
                ),
          boxShadow: [
            BoxShadow(
              color: isDark
                  ? Colors.white.withValues(alpha: 0.02)
                  : Colors.black.withValues(alpha: 0.04),
              offset: const Offset(0, 2),
              blurRadius: 4,
              spreadRadius: 0,
            ),
          ],
        ),
        child: Padding(
          padding: EdgeInsets.all(3.w),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              // Category Icon with colored background
              Container(
                width: 12.w,
                height: 6.h,
                decoration: BoxDecoration(
                  color: (isIncome
                          ? AppTheme.getSuccessColor(isDark)
                          : AppTheme.getErrorColor(isDark))
                      .withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Center(
                  child: CustomIconWidget(
                    iconName: category['icon'] as String,
                    color: isIncome
                        ? AppTheme.getSuccessColor(isDark)
                        : AppTheme.getErrorColor(isDark),
                    size: 6.w,
                  ),
                ),
              ),

              SizedBox(height: 1.h),

              // Category Name
              Flexible(
                child: Text(
                  category['name'] as String,
                  style: theme.textTheme.titleSmall?.copyWith(
                    fontWeight: FontWeight.w500,
                    color: isDark ? Colors.white : const Color(0xFF212121),
                  ),
                  textAlign: TextAlign.center,
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
              ),

              SizedBox(height: 0.5.h),

              // Usage Statistics
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  CustomIconWidget(
                    iconName: 'receipt_long',
                    color: isDark
                        ? const Color(0xFFB0B0B0)
                        : const Color(0xFF757575),
                    size: 3.w,
                  ),
                  SizedBox(width: 1.w),
                  Flexible(
                    child: Text(
                      '${category['transactionCount']} uses',
                      style: theme.textTheme.bodySmall?.copyWith(
                        color: isDark
                            ? const Color(0xFFB0B0B0)
                            : const Color(0xFF757575),
                        fontSize: 10.sp,
                      ),
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                ],
              ),

              // System Badge for default categories
              if (category['isDefault'] == true) ...[
                SizedBox(height: 0.5.h),
                Container(
                  padding:
                      EdgeInsets.symmetric(horizontal: 2.w, vertical: 0.2.h),
                  decoration: BoxDecoration(
                    color:
                        AppTheme.getAccentColor(isDark).withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Text(
                    'Default',
                    style: theme.textTheme.labelSmall?.copyWith(
                      color: AppTheme.getAccentColor(isDark),
                      fontSize: 8.sp,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }
}
