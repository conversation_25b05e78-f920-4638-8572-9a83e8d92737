import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:sizer/sizer.dart';

import '../../core/app_export.dart';
import './widgets/add_category_modal_widget.dart';
import './widgets/category_card_widget.dart';
import './widgets/category_context_menu_widget.dart';
import './widgets/category_search_widget.dart';
import './widgets/empty_categories_widget.dart';

class CategoriesManagement extends StatefulWidget {
  const CategoriesManagement({super.key});

  @override
  State<CategoriesManagement> createState() => _CategoriesManagementState();
}

class _CategoriesManagementState extends State<CategoriesManagement>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  int _currentTabIndex = 0;
  String _searchQuery = '';
  CategoryModel? _selectedCategory;
  OverlayEntry? _contextMenuOverlay;

  // Data
  List<CategoryModel> _categories = [];
  bool _isLoading = true;
  String? _error;

  // Services
  late DataService _dataService;
  late AuthService _authService;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
    _tabController.addListener(() {
      if (_tabController.indexIsChanging) {
        setState(() {
          _currentTabIndex = _tabController.index;
          _searchQuery = '';
        });
        _hideContextMenu();
      }
    });

    _dataService = DataService.instance;
    _authService = AuthService.instance;
    _loadCategories();
  }

  @override
  void dispose() {
    _tabController.dispose();
    _hideContextMenu();
    super.dispose();
  }

  Future<void> _loadCategories() async {
    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      _categories = await _dataService.getAllCategories();

      setState(() {
        _isLoading = false;
      });
    } catch (error) {
      setState(() {
        _error = error.toString();
        _isLoading = false;
      });
    }
  }

  // Convert CategoryModel to Map for widget compatibility
  Map<String, dynamic> _convertCategoryToMap(CategoryModel category) {
    return {
      'id': category.id,
      'name': category.name,
      'icon': category.icon ?? 'category',
      'color': category.color != null
          ? int.tryParse(category.color!) ?? 0xFF1B365D
          : 0xFF1B365D,
      'type': category.type.name,
      'isDefault': false, // TODO: Implement default categories
      'transactionCount': 0, // TODO: Get transaction count
      'createdAt': category.createdAt,
    };
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    // Show loading state
    if (_isLoading) {
      return Scaffold(
        backgroundColor: theme.scaffoldBackgroundColor,
        appBar: AppBar(
          title: const Text('Categories Management'),
          backgroundColor: theme.cardColor,
        ),
        body: const Center(
          child: CircularProgressIndicator(),
        ),
      );
    }

    // Show error state
    if (_error != null) {
      return Scaffold(
        backgroundColor: theme.scaffoldBackgroundColor,
        appBar: AppBar(
          title: const Text('Categories Management'),
          backgroundColor: theme.cardColor,
        ),
        body: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.error_outline,
                size: 64,
                color: theme.colorScheme.error,
              ),
              SizedBox(height: 2.h),
              Text(
                'Failed to load categories',
                style: theme.textTheme.headlineSmall,
              ),
              SizedBox(height: 1.h),
              Text(
                _error!,
                style: theme.textTheme.bodyMedium,
                textAlign: TextAlign.center,
              ),
              SizedBox(height: 3.h),
              ElevatedButton(
                onPressed: _loadCategories,
                child: const Text('Retry'),
              ),
            ],
          ),
        ),
      );
    }

    return Scaffold(
      backgroundColor: theme.scaffoldBackgroundColor,
      appBar: AppBar(
        title: Text(
          'Categories Management',
          style: theme.textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        backgroundColor: theme.cardColor,
        elevation: 1,
        shadowColor: isDark
            ? Colors.white.withValues(alpha: 0.05)
            : Colors.black.withValues(alpha: 0.04),
        leading: IconButton(
          icon: CustomIconWidget(
            iconName: 'arrow_back',
            color: isDark ? Colors.white : const Color(0xFF212121),
            size: 6.w,
          ),
          onPressed: () => Navigator.of(context).pop(),
        ),
        actions: [
          IconButton(
            icon: CustomIconWidget(
              iconName: 'analytics',
              color: isDark ? Colors.white : const Color(0xFF212121),
              size: 6.w,
            ),
            onPressed: _showCategoryAnalytics,
            tooltip: 'Category Analytics',
          ),
        ],
        bottom: PreferredSize(
          preferredSize: Size.fromHeight(6.h),
          child: Container(
            color: theme.cardColor,
            child: TabBar(
              controller: _tabController,
              indicatorColor: _currentTabIndex == 0
                  ? AppTheme.getSuccessColor(isDark)
                  : AppTheme.getErrorColor(isDark),
              labelColor: _currentTabIndex == 0
                  ? AppTheme.getSuccessColor(isDark)
                  : AppTheme.getErrorColor(isDark),
              unselectedLabelColor:
                  isDark ? const Color(0xFFB0B0B0) : const Color(0xFF757575),
              labelStyle: theme.textTheme.titleSmall?.copyWith(
                fontWeight: FontWeight.w600,
              ),
              unselectedLabelStyle: theme.textTheme.titleSmall?.copyWith(
                fontWeight: FontWeight.w400,
              ),
              tabs: const [
                Tab(text: 'INCOME'),
                Tab(text: 'EXPENSE'),
              ],
            ),
          ),
        ),
      ),
      body: Column(
        children: [
          // Search Bar
          CategorySearchWidget(
            onSearchChanged: (query) {
              setState(() {
                _searchQuery = query;
              });
              _hideContextMenu();
            },
            currentQuery: _searchQuery,
          ),

          // Categories Grid
          Expanded(
            child: TabBarView(
              controller: _tabController,
              children: [
                _buildCategoriesGrid(CategoryType.income),
                _buildCategoriesGrid(CategoryType.expense),
              ],
            ),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: _showAddCategoryModal,
        backgroundColor: _currentTabIndex == 0
            ? AppTheme.getSuccessColor(isDark)
            : AppTheme.getErrorColor(isDark),
        foregroundColor: Colors.white,
        icon: CustomIconWidget(
          iconName: 'add',
          color: Colors.white,
          size: 5.w,
        ),
        label: Text(
          'Add Category',
          style: theme.textTheme.labelLarge?.copyWith(
            color: Colors.white,
            fontWeight: FontWeight.w600,
          ),
        ),
      ),
    );
  }

  Widget _buildCategoriesGrid(CategoryType categoryType) {
    final filteredCategories = _getFilteredCategories(categoryType);

    if (filteredCategories.isEmpty) {
      return EmptyCategoriesWidget(
        categoryType: categoryType.name,
        onAddCategory: _showAddCategoryModal,
        isSearchResult: _searchQuery.isNotEmpty,
        searchQuery: _searchQuery,
      );
    }

    return RefreshIndicator(
      onRefresh: _refreshCategories,
      color: categoryType == CategoryType.income
          ? AppTheme.getSuccessColor(
              Theme.of(context).brightness == Brightness.dark)
          : AppTheme.getErrorColor(
              Theme.of(context).brightness == Brightness.dark),
      child: GridView.builder(
        padding: EdgeInsets.all(4.w),
        gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: 2,
          crossAxisSpacing: 3.w,
          mainAxisSpacing: 2.h,
          childAspectRatio: 0.85,
        ),
        itemCount: filteredCategories.length,
        itemBuilder: (context, index) {
          final category = filteredCategories[index];
          final categoryMap = _convertCategoryToMap(category);

          return CategoryCardWidget(
            category: categoryMap,
            isSelected: _selectedCategory?.id == category.id,
            onTap: () => _selectCategory(category),
            onLongPress: () => _showContextMenu(context, category),
          );
        },
      ),
    );
  }

  List<CategoryModel> _getFilteredCategories(CategoryType categoryType) {
    var filtered =
        _categories.where((category) => category.type == categoryType).toList();

    if (_searchQuery.isNotEmpty) {
      filtered = filtered
          .where((category) =>
              category.name.toLowerCase().contains(_searchQuery.toLowerCase()))
          .toList();
    }

    // Sort by name for now (TODO: implement usage count sorting)
    filtered.sort((a, b) => a.name.compareTo(b.name));

    return filtered;
  }

  void _selectCategory(CategoryModel category) {
    setState(() {
      _selectedCategory =
          _selectedCategory?.id == category.id ? null : category;
    });
    _hideContextMenu();
  }

  void _showContextMenu(BuildContext context, CategoryModel category) {
    _hideContextMenu();

    final RenderBox renderBox = context.findRenderObject() as RenderBox;
    final position = renderBox.localToGlobal(Offset.zero);
    final size = renderBox.size;

    _contextMenuOverlay = OverlayEntry(
      builder: (context) => Positioned(
        left: position.dx + size.width / 2 - 25.w,
        top: position.dy + size.height + 1.h,
        child: Material(
          color: Colors.transparent,
          child: CategoryContextMenuWidget(
            category: _convertCategoryToMap(category),
            onEdit: () => _editCategory(category),
            onDelete: () => _deleteCategory(category),
            onSetDefault: () => _setAsDefault(category),
            onViewTransactions: () => _viewTransactions(category),
            onClose: _hideContextMenu,
          ),
        ),
      ),
    );

    Overlay.of(context).insert(_contextMenuOverlay!);

    setState(() {
      _selectedCategory = category;
    });
  }

  void _hideContextMenu() {
    _contextMenuOverlay?.remove();
    _contextMenuOverlay = null;
    setState(() {
      _selectedCategory = null;
    });
  }

  void _showAddCategoryModal() {
    final categoryType = _currentTabIndex == 0 ? 'income' : 'expense';

    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => Padding(
        padding: EdgeInsets.only(
          bottom: MediaQuery.of(context).viewInsets.bottom,
        ),
        child: AddCategoryModalWidget(
          categoryType: categoryType,
          existingCategories: _categories.map(_convertCategoryToMap).toList(),
          onCategoryAdded: _addCategory,
        ),
      ),
    );
  }

  Future<void> _addCategory(Map<String, dynamic> newCategoryData) async {
    try {
      // TODO: Implement category creation when we have custom categories
      // For now, categories are read-only from templates

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content:
              Text('Custom categories will be available in a future update'),
          backgroundColor: AppTheme.getWarningColor(
              Theme.of(context).brightness == Brightness.dark),
        ),
      );
    } catch (error) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Failed to add category: $error'),
          backgroundColor: AppTheme.getErrorColor(
              Theme.of(context).brightness == Brightness.dark),
        ),
      );
    }
  }

  void _editCategory(CategoryModel category) {
    // TODO: Implement category editing
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Category editing will be available in a future update'),
        backgroundColor: AppTheme.getWarningColor(
            Theme.of(context).brightness == Brightness.dark),
      ),
    );
  }

  void _deleteCategory(CategoryModel category) {
    // TODO: Implement category deletion for custom categories
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Cannot delete template categories'),
        backgroundColor: AppTheme.getWarningColor(
            Theme.of(context).brightness == Brightness.dark),
      ),
    );
  }

  void _setAsDefault(CategoryModel category) {
    // TODO: Implement default category setting
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content:
            Text('Default categories will be available in a future update'),
        backgroundColor: AppTheme.getWarningColor(
            Theme.of(context).brightness == Brightness.dark),
      ),
    );
  }

  void _viewTransactions(CategoryModel category) {
    Navigator.pushNamed(
      context,
      '/transaction-history',
      arguments: {'categoryId': category.id},
    );
  }

  void _showCategoryAnalytics() {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    final incomeCategories =
        _categories.where((c) => c.type == CategoryType.income).length;
    final expenseCategories =
        _categories.where((c) => c.type == CategoryType.expense).length;

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: theme.cardColor,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
        title: Row(
          children: [
            CustomIconWidget(
              iconName: 'analytics',
              color: AppTheme.getAccentColor(isDark),
              size: 6.w,
            ),
            SizedBox(width: 3.w),
            Text(
              'Category Analytics',
              style: theme.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildAnalyticsRow('Total Categories', '${_categories.length}'),
            _buildAnalyticsRow('Income Categories', '$incomeCategories'),
            _buildAnalyticsRow('Expense Categories', '$expenseCategories'),
            _buildAnalyticsRow('Templates Available', 'Yes'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text(
              'Close',
              style: theme.textTheme.labelLarge?.copyWith(
                color: AppTheme.getAccentColor(isDark),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAnalyticsRow(String label, String value) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    return Padding(
      padding: EdgeInsets.symmetric(vertical: 1.h),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: theme.textTheme.bodyMedium?.copyWith(
              color: isDark ? const Color(0xFFB0B0B0) : const Color(0xFF757575),
            ),
          ),
          Text(
            value,
            style: theme.textTheme.bodyMedium?.copyWith(
              fontWeight: FontWeight.w600,
              color: isDark ? Colors.white : const Color(0xFF212121),
            ),
          ),
        ],
      ),
    );
  }

  Future<void> _refreshCategories() async {
    await _loadCategories();
  }
}
