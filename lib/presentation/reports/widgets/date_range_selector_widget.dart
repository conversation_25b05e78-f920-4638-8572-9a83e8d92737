import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';

import '../../../core/app_export.dart';
import '../../../theme/app_theme.dart';

class DateRangeSelectorWidget extends StatelessWidget {
  final int selectedIndex;
  final Function(int) onSelectionChanged;
  final Function() onCustomDateTap;

  const DateRangeSelectorWidget({
    super.key,
    required this.selectedIndex,
    required this.onSelectionChanged,
    required this.onCustomDateTap,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;
    final List<String> options = ['Today', 'Week', 'Month', 'Custom'];

    return Container(
      height: 6.h,
      padding: EdgeInsets.symmetric(horizontal: 4.w),
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        boxShadow: [
          BoxShadow(
            color: isDark
                ? Colors.white.withValues(alpha: 0.05)
                : Colors.black.withValues(alpha: 0.04),
            offset: const Offset(0, 2),
            blurRadius: 4,
            spreadRadius: 0,
          ),
        ],
      ),
      child: Row(
        children: List.generate(options.length, (index) {
          final isSelected = index == selectedIndex;
          final isCustom = index == 3;

          return Expanded(
            child: GestureDetector(
              onTap: () {
                if (isCustom) {
                  onCustomDateTap();
                } else {
                  onSelectionChanged(index);
                }
              },
              child: Container(
                margin: EdgeInsets.symmetric(horizontal: 1.w, vertical: 1.h),
                decoration: BoxDecoration(
                  color: isSelected
                      ? (isDark ? AppTheme.primaryDark : AppTheme.primaryLight)
                      : Colors.transparent,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Center(
                  child: Text(
                    options[index],
                    style: theme.textTheme.labelMedium?.copyWith(
                      color: isSelected
                          ? Colors.white
                          : (isDark
                              ? AppTheme.textSecondaryDark
                              : AppTheme.textSecondaryLight),
                      fontWeight:
                          isSelected ? FontWeight.w600 : FontWeight.w400,
                    ),
                  ),
                ),
              ),
            ),
          );
        }),
      ),
    );
  }
}
