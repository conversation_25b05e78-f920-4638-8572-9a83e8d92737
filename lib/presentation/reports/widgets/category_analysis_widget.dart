import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';

import '../../../core/app_export.dart';

class CategoryAnalysisWidget extends StatelessWidget {
  final List<Map<String, dynamic>> categoryData;

  const CategoryAnalysisWidget({
    super.key,
    required this.categoryData,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    return Container(
      padding: EdgeInsets.all(4.w),
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: isDark
                ? Colors.white.withValues(alpha: 0.05)
                : Colors.black.withValues(alpha: 0.04),
            offset: const Offset(0, 2),
            blurRadius: 8,
            spreadRadius: 0,
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Category Analysis',
                style: theme.textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
              GestureDetector(
                onTap: () {
                  // Handle view all categories
                },
                child: Text(
                  'View All',
                  style: theme.textTheme.bodySmall?.copyWith(
                    color:
                        isDark ? AppTheme.primaryDark : AppTheme.primaryLight,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ],
          ),
          SizedBox(height: 3.h),
          ...categoryData.map((category) => _buildCategoryItem(
                context,
                category['name'] as String,
                category['amount'] as double,
                category['percentage'] as double,
                category['color'] as Color,
                category['previousAmount'] as double,
                theme,
                isDark,
              )),
        ],
      ),
    );
  }

  Widget _buildCategoryItem(
    BuildContext context,
    String name,
    double amount,
    double percentage,
    Color color,
    double previousAmount,
    ThemeData theme,
    bool isDark,
  ) {
    final isIncrease = amount > previousAmount;
    final changePercentage = previousAmount > 0
        ? ((amount - previousAmount) / previousAmount * 100).abs()
        : 0.0;

    return Container(
      margin: EdgeInsets.only(bottom: 2.h),
      child: Column(
        children: [
          Row(
            children: [
              Container(
                width: 4.w,
                height: 4.w,
                decoration: BoxDecoration(
                  color: color,
                  shape: BoxShape.circle,
                ),
              ),
              SizedBox(width: 3.w),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          name,
                          style: theme.textTheme.bodyMedium?.copyWith(
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                        Text(
                          '\$${amount.toStringAsFixed(0)}',
                          style: AppTheme.currencyStyle(
                            isLight: !isDark,
                            fontSize: 14.sp,
                          ),
                        ),
                      ],
                    ),
                    SizedBox(height: 0.5.h),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          '${percentage.toStringAsFixed(1)}% of total',
                          style: theme.textTheme.bodySmall?.copyWith(
                            color: isDark
                                ? AppTheme.textSecondaryDark
                                : AppTheme.textSecondaryLight,
                          ),
                        ),
                        if (changePercentage > 0)
                          Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              CustomIconWidget(
                                iconName: isIncrease
                                    ? 'trending_up'
                                    : 'trending_down',
                                color: isIncrease
                                    ? AppTheme.getErrorColor(isDark)
                                    : AppTheme.getSuccessColor(isDark),
                                size: 12,
                              ),
                              SizedBox(width: 1.w),
                              Text(
                                '${changePercentage.toStringAsFixed(1)}%',
                                style: theme.textTheme.bodySmall?.copyWith(
                                  color: isIncrease
                                      ? AppTheme.getErrorColor(isDark)
                                      : AppTheme.getSuccessColor(isDark),
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ],
                          ),
                      ],
                    ),
                  ],
                ),
              ),
            ],
          ),
          SizedBox(height: 1.h),
          LinearProgressIndicator(
            value: percentage / 100,
            backgroundColor:
                isDark ? AppTheme.dividerDark : AppTheme.dividerLight,
            valueColor: AlwaysStoppedAnimation<Color>(color),
            minHeight: 0.5.h,
          ),
        ],
      ),
    );
  }
}
