import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';

import '../../core/app_export.dart';
import './widgets/category_analysis_widget.dart';
import './widgets/chart_section_widget.dart';
import './widgets/date_range_selector_widget.dart';
import './widgets/export_bottom_sheet_widget.dart';
import './widgets/payment_mode_analytics_widget.dart';
import './widgets/summary_card_widget.dart';

class Reports extends StatefulWidget {
  const Reports({super.key});

  @override
  State<Reports> createState() => _ReportsState();
}

class _ReportsState extends State<Reports> with TickerProviderStateMixin {
  int _selectedDateRange = 1; // 0: Today, 1: Week, 2: Month, 3: Custom
  int _selectedChartType = 0; // 0: Bar, 1: Pie, 2: Line
  DateTime _startDate = DateTime.now().subtract(const Duration(days: 7));
  DateTime _endDate = DateTime.now();

  // Data
  Map<String, dynamic>? _financialSummary;
  List<Map<String, dynamic>> _chartData = [];
  List<Map<String, dynamic>> _categoryData = [];
  List<Map<String, dynamic>> _paymentModeData = [];
  bool _isLoading = true;
  String? _error;
  String? _selectedCashbookId;

  // Services
  late ReportsService _reportsService;
  late DataService _dataService;

  // Mock financial data for fallback
  final List<Map<String, dynamic>> _summaryData = [
    {
      "title": "Total Income",
      "amount": "\$12,450",
      "percentage": "+8.2%",
      "isPositive": true,
      "color": const Color(0xFF4CAF50),
      "icon": Icons.trending_up,
    },
    {
      "title": "Total Expenses",
      "amount": "\$8,320",
      "percentage": "+3.1%",
      "isPositive": false,
      "color": const Color(0xFFF44336),
      "icon": Icons.trending_down,
    },
    {
      "title": "Net Profit",
      "amount": "\$4,130",
      "percentage": "+15.7%",
      "isPositive": true,
      "color": const Color(0xFF2196F3),
      "icon": Icons.account_balance_wallet,
    },
  ];

  final List<Map<String, dynamic>> _mockChartData = [
    {"month": "Jan", "income": 5200.0, "expense": 3100.0},
    {"month": "Feb", "income": 6800.0, "expense": 4200.0},
    {"month": "Mar", "income": 7500.0, "expense": 4800.0},
    {"month": "Apr", "income": 8200.0, "expense": 5100.0},
    {"month": "May", "income": 9100.0, "expense": 5600.0},
    {"month": "Jun", "income": 8900.0, "expense": 5400.0},
  ];

  final List<Map<String, dynamic>> _mockCategoryData = [
    {
      "name": "Food & Dining",
      "amount": 2850.0,
      "percentage": 34.3,
      "color": const Color(0xFF4CAF50),
      "previousAmount": 2650.0,
    },
    {
      "name": "Transportation",
      "amount": 1680.0,
      "percentage": 20.2,
      "color": const Color(0xFF2196F3),
      "previousAmount": 1750.0,
    },
    {
      "name": "Shopping",
      "amount": 1420.0,
      "percentage": 17.1,
      "color": const Color(0xFFFF9800),
      "previousAmount": 1200.0,
    },
    {
      "name": "Bills & Utilities",
      "amount": 1180.0,
      "percentage": 14.2,
      "color": const Color(0xFFF44336),
      "previousAmount": 1150.0,
    },
    {
      "name": "Entertainment",
      "amount": 890.0,
      "percentage": 10.7,
      "color": const Color(0xFF9C27B0),
      "previousAmount": 950.0,
    },
    {
      "name": "Healthcare",
      "amount": 300.0,
      "percentage": 3.6,
      "color": const Color(0xFF607D8B),
      "previousAmount": 280.0,
    },
  ];

  final List<Map<String, dynamic>> _mockPaymentModeData = [
    {
      "name": "Cash",
      "amount": 4850.0,
      "color": const Color(0xFF4CAF50),
      "icon": "money",
    },
    {
      "name": "Credit Card",
      "amount": 2180.0,
      "color": const Color(0xFF2196F3),
      "icon": "credit_card",
    },
    {
      "name": "Debit Card",
      "amount": 890.0,
      "color": const Color(0xFFFF9800),
      "icon": "payment",
    },
    {
      "name": "Digital Wallet",
      "amount": 400.0,
      "color": const Color(0xFF9C27B0),
      "icon": "account_balance_wallet",
    },
  ];

  @override
  void initState() {
    super.initState();
    _reportsService = ReportsService.instance;
    _dataService = DataService.instance;
    _loadReportsData();
  }

  Future<void> _loadReportsData() async {
    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      final results = await Future.wait([
        _reportsService.getFinancialSummary(
          cashbookId: _selectedCashbookId,
          startDate: _startDate,
          endDate: _endDate,
        ),
        _reportsService.getMonthlyTrends(
          cashbookId: _selectedCashbookId,
          months: 6,
        ),
        _reportsService.getCategoryAnalysis(
          cashbookId: _selectedCashbookId,
          startDate: _startDate,
          endDate: _endDate,
          type: TransactionType.expense,
        ),
        _reportsService.getPaymentModeAnalysis(
          cashbookId: _selectedCashbookId,
          startDate: _startDate,
          endDate: _endDate,
        ),
      ]);

      setState(() {
        _financialSummary = results[0] as Map<String, dynamic>;
        _chartData = results[1] as List<Map<String, dynamic>>;
        _categoryData = results[2] as List<Map<String, dynamic>>;
        _paymentModeData = results[3] as List<Map<String, dynamic>>;
        _isLoading = false;
      });
    } catch (error) {
      setState(() {
        _error = error.toString();
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    return Scaffold(
      backgroundColor: theme.scaffoldBackgroundColor,
      appBar: AppBar(
        title: Text(
          'Reports',
          style: theme.textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        backgroundColor: theme.colorScheme.surface,
        elevation: 0,
        actions: [
          IconButton(
            onPressed: _showExportBottomSheet,
            icon: CustomIconWidget(
              iconName: 'share',
              color:
                  isDark ? AppTheme.textPrimaryDark : AppTheme.textPrimaryLight,
              size: 24,
            ),
            tooltip: 'Export Report',
          ),
          IconButton(
            onPressed: () {
              // Handle filter options
              _showFilterOptions();
            },
            icon: CustomIconWidget(
              iconName: 'filter_list',
              color:
                  isDark ? AppTheme.textPrimaryDark : AppTheme.textPrimaryLight,
              size: 24,
            ),
            tooltip: 'Filter',
          ),
        ],
      ),
      body: SafeArea(
        child: _isLoading
            ? const Center(child: CircularProgressIndicator())
            : _error != null
                ? Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          Icons.error_outline,
                          size: 64,
                          color: theme.colorScheme.error,
                        ),
                        SizedBox(height: 2.h),
                        Text(
                          'Failed to load reports',
                          style: theme.textTheme.headlineSmall,
                        ),
                        SizedBox(height: 1.h),
                        Text(
                          _error!,
                          style: theme.textTheme.bodyMedium,
                          textAlign: TextAlign.center,
                        ),
                        SizedBox(height: 3.h),
                        ElevatedButton(
                          onPressed: _loadReportsData,
                          child: const Text('Retry'),
                        ),
                      ],
                    ),
                  )
                : Column(
                    children: [
                      // Sticky Date Range Selector
                      DateRangeSelectorWidget(
                        selectedIndex: _selectedDateRange,
                        onSelectionChanged: (index) {
                          setState(() {
                            _selectedDateRange = index;
                            _updateDateRange();
                          });
                        },
                        onCustomDateTap: _showCustomDatePicker,
                      ),

                      // Scrollable Content
                      Expanded(
                        child: RefreshIndicator(
                          onRefresh: _loadReportsData,
                          child: SingleChildScrollView(
                            padding: EdgeInsets.all(4.w),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                // Summary Cards
                                Row(
                                  mainAxisAlignment:
                                      MainAxisAlignment.spaceBetween,
                                  children: _buildSummaryCards(),
                                ),

                                SizedBox(height: 4.h),

                                // Charts Section
                                ChartSectionWidget(
                                  selectedChartType: _selectedChartType,
                                  onChartTypeChanged: (index) {
                                    setState(() {
                                      _selectedChartType = index;
                                    });
                                  },
                                  chartData: _chartData.isNotEmpty
                                      ? _chartData
                                      : _mockChartData,
                                ),

                                SizedBox(height: 4.h),

                                // Category Analysis
                                CategoryAnalysisWidget(
                                  categoryData: _categoryData.isNotEmpty
                                      ? _categoryData
                                      : _mockCategoryData,
                                ),

                                SizedBox(height: 4.h),

                                // Payment Mode Analytics
                                PaymentModeAnalyticsWidget(
                                  paymentModeData: _paymentModeData.isNotEmpty
                                      ? _paymentModeData
                                      : _mockPaymentModeData,
                                ),

                                SizedBox(height: 4.h),

                                // Additional Insights
                                _buildInsightsSection(theme, isDark),

                                SizedBox(height: 2.h),
                              ],
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
      ),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: () {
          Navigator.pushNamed(context, '/add-transaction');
        },
        icon: CustomIconWidget(
          iconName: 'add',
          color: Colors.white,
          size: 20,
        ),
        label: Text(
          'Add Transaction',
          style: theme.textTheme.labelMedium?.copyWith(
            color: Colors.white,
            fontWeight: FontWeight.w500,
          ),
        ),
        backgroundColor: isDark ? AppTheme.primaryDark : AppTheme.primaryLight,
      ),
    );
  }

  List<Widget> _buildSummaryCards() {
    if (_financialSummary == null) {
      return _summaryData.map((data) {
        return SummaryCardWidget(
          title: data['title'] as String,
          amount: data['amount'] as String,
          percentage: data['percentage'] as String,
          isPositive: data['isPositive'] as bool,
          cardColor: data['color'] as Color,
          icon: data['icon'] as IconData,
        );
      }).toList();
    }

    final summary = _financialSummary!;
    return [
      SummaryCardWidget(
        title: "Total Income",
        amount: "\$${summary['totalIncome'].toStringAsFixed(2)}",
        percentage: "${summary['incomeChange'].toStringAsFixed(1)}%",
        isPositive: summary['incomeChange'] >= 0,
        cardColor: const Color(0xFF4CAF50),
        icon: Icons.trending_up,
      ),
      SummaryCardWidget(
        title: "Total Expenses",
        amount: "\$${summary['totalExpenses'].toStringAsFixed(2)}",
        percentage: "${summary['expensesChange'].toStringAsFixed(1)}%",
        isPositive: summary['expensesChange'] <= 0, // Lower expenses are good
        cardColor: const Color(0xFFF44336),
        icon: Icons.trending_down,
      ),
      SummaryCardWidget(
        title: "Net Profit",
        amount: "\$${summary['netProfit'].toStringAsFixed(2)}",
        percentage: "${summary['profitChange'].toStringAsFixed(1)}%",
        isPositive: summary['profitChange'] >= 0,
        cardColor: const Color(0xFF2196F3),
        icon: Icons.account_balance_wallet,
      ),
    ];
  }

  Widget _buildInsightsSection(ThemeData theme, bool isDark) {
    return Container(
      padding: EdgeInsets.all(4.w),
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: isDark
                ? Colors.white.withValues(alpha: 0.05)
                : Colors.black.withValues(alpha: 0.04),
            offset: const Offset(0, 2),
            blurRadius: 8,
            spreadRadius: 0,
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              CustomIconWidget(
                iconName: 'lightbulb_outline',
                color: isDark ? AppTheme.warningDark : AppTheme.warningLight,
                size: 20,
              ),
              SizedBox(width: 2.w),
              Text(
                'Financial Insights',
                style: theme.textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
          SizedBox(height: 2.h),
          _buildInsightItem(
            'Your food expenses increased by 7.5% this month',
            'Consider setting a monthly food budget to control spending',
            Icons.restaurant,
            AppTheme.getWarningColor(isDark),
            theme,
            isDark,
          ),
          SizedBox(height: 2.h),
          _buildInsightItem(
            'You saved \$320 on transportation this month',
            'Great job! Keep using public transport or carpooling',
            Icons.directions_bus,
            AppTheme.getSuccessColor(isDark),
            theme,
            isDark,
          ),
          SizedBox(height: 2.h),
          _buildInsightItem(
            'Your income is 15% higher than last month',
            'Consider increasing your savings rate to 20%',
            Icons.trending_up,
            AppTheme.getSuccessColor(isDark),
            theme,
            isDark,
          ),
        ],
      ),
    );
  }

  Widget _buildInsightItem(
    String title,
    String description,
    IconData icon,
    Color color,
    ThemeData theme,
    bool isDark,
  ) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Container(
          padding: EdgeInsets.all(2.w),
          decoration: BoxDecoration(
            color: color.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(8),
          ),
          child: CustomIconWidget(
            iconName: icon.codePoint.toString(),
            color: color,
            size: 16,
          ),
        ),
        SizedBox(width: 3.w),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                title,
                style: theme.textTheme.bodyMedium?.copyWith(
                  fontWeight: FontWeight.w500,
                ),
              ),
              SizedBox(height: 0.5.h),
              Text(
                description,
                style: theme.textTheme.bodySmall?.copyWith(
                  color: isDark
                      ? AppTheme.textSecondaryDark
                      : AppTheme.textSecondaryLight,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  void _updateDateRange() {
    final now = DateTime.now();
    switch (_selectedDateRange) {
      case 0: // Today
        _startDate = DateTime(now.year, now.month, now.day);
        _endDate = now;
        break;
      case 1: // Week
        _startDate = now.subtract(const Duration(days: 7));
        _endDate = now;
        break;
      case 2: // Month
        _startDate = DateTime(now.year, now.month - 1, now.day);
        _endDate = now;
        break;
      case 3: // Custom - handled by date picker
        break;
    }
    _loadReportsData(); // Reload data when date range changes
  }

  void _showCustomDatePicker() async {
    final DateTimeRange? picked = await showDateRangePicker(
      context: context,
      firstDate: DateTime(2020),
      lastDate: DateTime.now(),
      initialDateRange: DateTimeRange(start: _startDate, end: _endDate),
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: Theme.of(context).colorScheme.copyWith(
                  primary: Theme.of(context).brightness == Brightness.dark
                      ? AppTheme.primaryDark
                      : AppTheme.primaryLight,
                ),
          ),
          child: child!,
        );
      },
    );

    if (picked != null) {
      setState(() {
        _startDate = picked.start;
        _endDate = picked.end;
        _selectedDateRange = 3;
      });
      _loadReportsData(); // Reload data when custom date range is selected
    }
  }

  void _showFilterOptions() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        padding: EdgeInsets.all(4.w),
        decoration: BoxDecoration(
          color: Theme.of(context).colorScheme.surface,
          borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Center(
              child: Container(
                width: 12.w,
                height: 0.5.h,
                decoration: BoxDecoration(
                  color: Theme.of(context).brightness == Brightness.dark
                      ? AppTheme.dividerDark
                      : AppTheme.dividerLight,
                  borderRadius: BorderRadius.circular(2),
                ),
              ),
            ),
            SizedBox(height: 3.h),
            Text(
              'Filter Options',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
            ),
            SizedBox(height: 3.h),
            ListTile(
              leading: CustomIconWidget(
                iconName: 'category',
                color: Theme.of(context).brightness == Brightness.dark
                    ? AppTheme.textPrimaryDark
                    : AppTheme.textPrimaryLight,
                size: 24,
              ),
              title: const Text('Filter by Category'),
              trailing: CustomIconWidget(
                iconName: 'arrow_forward_ios',
                color: Theme.of(context).brightness == Brightness.dark
                    ? AppTheme.textSecondaryDark
                    : AppTheme.textSecondaryLight,
                size: 16,
              ),
              onTap: () {
                Navigator.pop(context);
                // Handle category filter
              },
            ),
            ListTile(
              leading: CustomIconWidget(
                iconName: 'payment',
                color: Theme.of(context).brightness == Brightness.dark
                    ? AppTheme.textPrimaryDark
                    : AppTheme.textPrimaryLight,
                size: 24,
              ),
              title: const Text('Filter by Payment Mode'),
              trailing: CustomIconWidget(
                iconName: 'arrow_forward_ios',
                color: Theme.of(context).brightness == Brightness.dark
                    ? AppTheme.textSecondaryDark
                    : AppTheme.textSecondaryLight,
                size: 16,
              ),
              onTap: () {
                Navigator.pop(context);
                // Handle payment mode filter
              },
            ),
            ListTile(
              leading: CustomIconWidget(
                iconName: 'account_balance_wallet',
                color: Theme.of(context).brightness == Brightness.dark
                    ? AppTheme.textPrimaryDark
                    : AppTheme.textPrimaryLight,
                size: 24,
              ),
              title: const Text('Filter by Cashbook'),
              trailing: CustomIconWidget(
                iconName: 'arrow_forward_ios',
                color: Theme.of(context).brightness == Brightness.dark
                    ? AppTheme.textSecondaryDark
                    : AppTheme.textSecondaryLight,
                size: 16,
              ),
              onTap: () {
                Navigator.pop(context);
                // Handle cashbook filter
              },
            ),
            SizedBox(height: 4.h),
          ],
        ),
      ),
    );
  }

  void _showExportBottomSheet() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => ExportBottomSheetWidget(
        onExportSelected: _handleExport,
      ),
    );
  }

  Future<void> _handleExport(String format) async {
    try {
      String filePath;
      String message;

      switch (format) {
        case 'pdf':
          filePath = await _reportsService.exportToPDF(
            cashbookId: _selectedCashbookId,
            startDate: _startDate,
            endDate: _endDate,
            reportType: 'summary',
          );
          message = 'PDF report generated successfully';
          break;
        case 'csv':
          filePath = await _reportsService.exportToCSV(
            cashbookId: _selectedCashbookId,
            startDate: _startDate,
            endDate: _endDate,
            reportType: 'transactions',
          );
          message = 'CSV file exported successfully';
          break;
        default:
          throw Exception('Unsupported export format');
      }

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(message),
            action: SnackBarAction(
              label: 'Share',
              onPressed: () async {
                await _reportsService.shareFile(filePath, 'Cashbook Report');
              },
            ),
          ),
        );
      }
    } catch (error) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Export failed: $error'),
            backgroundColor: Theme.of(context).colorScheme.error,
          ),
        );
      }
    }
  }
}
