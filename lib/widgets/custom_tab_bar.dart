import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

/// Enum defining different tab bar variants for various contexts
enum CustomTabBarVariant {
  /// Standard tab bar with normal height and styling
  standard,

  /// Large tab bar with increased height and font size
  large,

  /// Compact tab bar with reduced height and font size
  compact,

  /// Pill-style tab bar with background indicators
  pill,
}

/// Custom Tab Bar implementing Contemporary Financial Minimalism design
/// with clear state indication and professional styling for financial applications.
class CustomTabBar extends StatefulWidget implements PreferredSizeWidget {
  /// List of tab labels
  final List<String> tabs;

  /// Current selected tab index
  final int currentIndex;

  /// Callback when a tab is selected
  final ValueChanged<int> onTap;

  /// Whether tabs are scrollable
  final bool isScrollable;

  /// Tab alignment when not scrollable
  final TabAlignment tabAlignment;

  /// Indicator color
  final Color? indicatorColor;

  /// Label color for selected tab
  final Color? labelColor;

  /// Label color for unselected tabs
  final Color? unselectedLabelColor;

  /// Background color of the tab bar
  final Color? backgroundColor;

  /// Tab bar variant for different contexts
  final CustomTabBarVariant variant;

  /// Whether to show divider below tab bar
  final bool showDivider;

  const CustomTabBar({
    super.key,
    required this.tabs,
    required this.currentIndex,
    required this.onTap,
    this.isScrollable = false,
    this.tabAlignment = TabAlignment.center,
    this.indicatorColor,
    this.labelColor,
    this.unselectedLabelColor,
    this.backgroundColor,
    this.variant = CustomTabBarVariant.standard,
    this.showDivider = true,
  });

  @override
  State<CustomTabBar> createState() => _CustomTabBarState();

  @override
  Size get preferredSize => Size.fromHeight(_getTabBarHeight());

  /// Get tab bar height based on variant
  double _getTabBarHeight() {
    switch (variant) {
      case CustomTabBarVariant.large:
        return 56.0;
      case CustomTabBarVariant.compact:
        return 40.0;
      case CustomTabBarVariant.standard:
      case CustomTabBarVariant.pill:
      default:
        return 48.0;
    }
  }

  /// Factory constructor for transaction history tabs
  factory CustomTabBar.transactionHistory({
    Key? key,
    required int currentIndex,
    required ValueChanged<int> onTap,
  }) {
    return CustomTabBar(
      key: key,
      tabs: const ['All', 'Income', 'Expense'],
      currentIndex: currentIndex,
      onTap: onTap,
      isScrollable: false,
      variant: CustomTabBarVariant.standard,
    );
  }

  /// Factory constructor for reports tabs
  factory CustomTabBar.reports({
    Key? key,
    required int currentIndex,
    required ValueChanged<int> onTap,
  }) {
    return CustomTabBar(
      key: key,
      tabs: const ['Overview', 'Income', 'Expenses', 'Categories'],
      currentIndex: currentIndex,
      onTap: onTap,
      isScrollable: true,
      variant: CustomTabBarVariant.standard,
    );
  }

  /// Factory constructor for categories management tabs
  factory CustomTabBar.categories({
    Key? key,
    required int currentIndex,
    required ValueChanged<int> onTap,
  }) {
    return CustomTabBar(
      key: key,
      tabs: const ['Income', 'Expense'],
      currentIndex: currentIndex,
      onTap: onTap,
      isScrollable: false,
      variant: CustomTabBarVariant.pill,
    );
  }

  /// Factory constructor for cashbook management tabs
  factory CustomTabBar.cashbook({
    Key? key,
    required int currentIndex,
    required ValueChanged<int> onTap,
  }) {
    return CustomTabBar(
      key: key,
      tabs: const ['Accounts', 'Cards', 'Cash'],
      currentIndex: currentIndex,
      onTap: onTap,
      isScrollable: false,
      variant: CustomTabBarVariant.standard,
    );
  }

  /// Factory constructor for dashboard period selector
  factory CustomTabBar.period({
    Key? key,
    required int currentIndex,
    required ValueChanged<int> onTap,
  }) {
    return CustomTabBar(
      key: key,
      tabs: const ['Week', 'Month', 'Year'],
      currentIndex: currentIndex,
      onTap: onTap,
      isScrollable: false,
      variant: CustomTabBarVariant.compact,
      showDivider: false,
    );
  }
}

class _CustomTabBarState extends State<CustomTabBar>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(
      length: widget.tabs.length,
      vsync: this,
      initialIndex: widget.currentIndex,
    );
    _tabController.addListener(_handleTabChange);
  }

  @override
  void didUpdateWidget(CustomTabBar oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.currentIndex != widget.currentIndex) {
      _tabController.animateTo(widget.currentIndex);
    }
  }

  @override
  void dispose() {
    _tabController.removeListener(_handleTabChange);
    _tabController.dispose();
    super.dispose();
  }

  void _handleTabChange() {
    if (_tabController.indexIsChanging) {
      widget.onTap(_tabController.index);
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;
    final isDark = theme.brightness == Brightness.dark;

    // Determine effective colors
    final effectiveBackgroundColor =
        widget.backgroundColor ?? colorScheme.surface;
    final effectiveIndicatorColor = widget.indicatorColor ??
        (isDark ? const Color(0xFF4A90E2) : const Color(0xFF1B365D));
    final effectiveLabelColor = widget.labelColor ??
        (isDark ? const Color(0xFF4A90E2) : const Color(0xFF1B365D));
    final effectiveUnselectedLabelColor = widget.unselectedLabelColor ??
        (isDark ? const Color(0xFFB0B0B0) : const Color(0xFF757575));

    return Container(
      color: effectiveBackgroundColor,
      child: Column(
        children: [
          _buildTabBar(
            theme,
            effectiveIndicatorColor,
            effectiveLabelColor,
            effectiveUnselectedLabelColor,
          ),
          if (widget.showDivider)
            Divider(
              height: 1,
              thickness: 1,
              color: isDark ? const Color(0xFF424242) : const Color(0xFFE0E0E0),
            ),
        ],
      ),
    );
  }

  /// Build the appropriate tab bar based on variant
  Widget _buildTabBar(
    ThemeData theme,
    Color indicatorColor,
    Color labelColor,
    Color unselectedLabelColor,
  ) {
    switch (widget.variant) {
      case CustomTabBarVariant.pill:
        return _buildPillTabBar(
          theme,
          indicatorColor,
          labelColor,
          unselectedLabelColor,
        );
      case CustomTabBarVariant.standard:
      case CustomTabBarVariant.large:
      case CustomTabBarVariant.compact:
      default:
        return _buildStandardTabBar(
          indicatorColor,
          labelColor,
          unselectedLabelColor,
        );
    }
  }

  /// Build standard tab bar with underline indicator
  Widget _buildStandardTabBar(
    Color indicatorColor,
    Color labelColor,
    Color unselectedLabelColor,
  ) {
    return TabBar(
      controller: _tabController,
      isScrollable: widget.isScrollable,
      tabAlignment: widget.tabAlignment,
      indicatorColor: indicatorColor,
      indicatorWeight: 2.0,
      indicatorSize: TabBarIndicatorSize.label,
      labelColor: labelColor,
      unselectedLabelColor: unselectedLabelColor,
      labelStyle: GoogleFonts.inter(
        fontSize: _getLabelFontSize(),
        fontWeight: FontWeight.w600, // SemiBold for selected state
        letterSpacing: 0.1,
      ),
      unselectedLabelStyle: GoogleFonts.inter(
        fontSize: _getLabelFontSize(),
        fontWeight: FontWeight.w400, // Regular for unselected state
        letterSpacing: 0.1,
      ),
      tabs: widget.tabs.map((tab) => Tab(text: tab)).toList(),
    );
  }

  /// Build pill-style tab bar with background indicators
  Widget _buildPillTabBar(
    ThemeData theme,
    Color indicatorColor,
    Color labelColor,
    Color unselectedLabelColor,
  ) {
    return Container(
      height: widget.preferredSize.height,
      padding: const EdgeInsets.all(4.0),
      decoration: BoxDecoration(
        color: theme.brightness == Brightness.dark
            ? const Color(0xFF2D2D2D)
            : const Color(0xFFF5F5F5),
        borderRadius: BorderRadius.circular(8.0),
      ),
      child: Row(
        children: List.generate(widget.tabs.length, (index) {
          final isSelected = index == widget.currentIndex;

          return Expanded(
            child: GestureDetector(
              onTap: () => widget.onTap(index),
              child: AnimatedContainer(
                duration: const Duration(milliseconds: 200),
                decoration: BoxDecoration(
                  color: isSelected ? indicatorColor : Colors.transparent,
                  borderRadius: BorderRadius.circular(6.0),
                ),
                child: Center(
                  child: Text(
                    widget.tabs[index],
                    style: GoogleFonts.inter(
                      fontSize: _getLabelFontSize(),
                      fontWeight:
                          isSelected ? FontWeight.w600 : FontWeight.w400,
                      color: isSelected ? Colors.white : unselectedLabelColor,
                      letterSpacing: 0.1,
                    ),
                  ),
                ),
              ),
            ),
          );
        }),
      ),
    );
  }

  /// Get label font size based on variant
  double _getLabelFontSize() {
    switch (widget.variant) {
      case CustomTabBarVariant.large:
        return 16.0;
      case CustomTabBarVariant.compact:
        return 12.0;
      case CustomTabBarVariant.standard:
      case CustomTabBarVariant.pill:
      default:
        return 14.0;
    }
  }
}
