import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

/// Custom AppBar widget implementing Contemporary Financial Minimalism design
/// with professional trust and clean hierarchy for financial applications.
class CustomAppBar extends StatelessWidget implements PreferredSizeWidget {
  /// The title to display in the app bar
  final String title;

  /// Optional leading widget (back button, menu icon, etc.)
  final Widget? leading;

  /// List of action widgets to display on the right side
  final List<Widget>? actions;

  /// Whether to show the back button automatically
  final bool automaticallyImplyLeading;

  /// Background color of the app bar
  final Color? backgroundColor;

  /// Foreground color for text and icons
  final Color? foregroundColor;

  /// Elevation of the app bar (minimal by design)
  final double elevation;

  /// Whether to center the title
  final bool centerTitle;

  /// Custom title widget (overrides title string)
  final Widget? titleWidget;

  /// App bar variant for different contexts
  final CustomAppBarVariant variant;

  const CustomAppBar({
    super.key,
    required this.title,
    this.leading,
    this.actions,
    this.automaticallyImplyLeading = true,
    this.backgroundColor,
    this.foregroundColor,
    this.elevation = 1.0,
    this.centerTitle = false,
    this.titleWidget,
    this.variant = CustomAppBarVariant.standard,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;
    final isDark = theme.brightness == Brightness.dark;

    // Determine colors based on variant and theme
    final effectiveBackgroundColor = backgroundColor ??
        (variant == CustomAppBarVariant.transparent
            ? Colors.transparent
            : colorScheme.surface);

    final effectiveForegroundColor =
        foregroundColor ?? (isDark ? Colors.white : const Color(0xFF212121));

    // Determine elevation based on variant
    final effectiveElevation =
        variant == CustomAppBarVariant.transparent ? 0.0 : elevation;

    return AppBar(
      title: titleWidget ??
          Text(
            title,
            style: GoogleFonts.inter(
              fontSize: _getTitleFontSize(),
              fontWeight: FontWeight.w600,
              color: effectiveForegroundColor,
              letterSpacing: 0.15,
            ),
          ),
      leading: leading,
      actions: actions,
      automaticallyImplyLeading: automaticallyImplyLeading,
      backgroundColor: effectiveBackgroundColor,
      foregroundColor: effectiveForegroundColor,
      elevation: effectiveElevation,
      shadowColor: isDark
          ? Colors.white.withValues(alpha: 0.1)
          : Colors.black.withValues(alpha: 0.04),
      centerTitle: centerTitle,
      titleSpacing: 16.0,
      toolbarHeight: _getToolbarHeight(),
      iconTheme: IconThemeData(
        color: effectiveForegroundColor,
        size: 24.0,
      ),
      actionsIconTheme: IconThemeData(
        color: effectiveForegroundColor,
        size: 24.0,
      ),
    );
  }

  /// Get title font size based on variant
  double _getTitleFontSize() {
    switch (variant) {
      case CustomAppBarVariant.large:
        return 24.0;
      case CustomAppBarVariant.small:
        return 18.0;
      case CustomAppBarVariant.standard:
      case CustomAppBarVariant.transparent:
      default:
        return 20.0;
    }
  }

  /// Get toolbar height based on variant
  double _getToolbarHeight() {
    switch (variant) {
      case CustomAppBarVariant.large:
        return 64.0;
      case CustomAppBarVariant.small:
        return 48.0;
      case CustomAppBarVariant.standard:
      case CustomAppBarVariant.transparent:
      default:
        return 56.0;
    }
  }

  @override
  Size get preferredSize => Size.fromHeight(_getToolbarHeight());

  /// Factory constructor for dashboard app bar with specific actions
  factory CustomAppBar.dashboard({
    Key? key,
    required BuildContext context,
    String title = 'Dashboard',
  }) {
    return CustomAppBar(
      key: key,
      title: title,
      centerTitle: false,
      actions: [
        IconButton(
          icon: const Icon(Icons.notifications_outlined),
          onPressed: () {
            // Handle notifications
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(content: Text('Notifications')),
            );
          },
          tooltip: 'Notifications',
        ),
        IconButton(
          icon: const Icon(Icons.account_circle_outlined),
          onPressed: () {
            // Handle profile
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(content: Text('Profile')),
            );
          },
          tooltip: 'Profile',
        ),
      ],
    );
  }

  /// Factory constructor for transaction-related screens
  factory CustomAppBar.transaction({
    Key? key,
    required BuildContext context,
    required String title,
    bool showSave = false,
  }) {
    return CustomAppBar(
      key: key,
      title: title,
      centerTitle: false,
      actions: showSave
          ? [
              TextButton(
                onPressed: () {
                  // Handle save action
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(content: Text('Saved')),
                  );
                },
                child: Text(
                  'Save',
                  style: GoogleFonts.inter(
                    fontWeight: FontWeight.w500,
                    fontSize: 14,
                  ),
                ),
              ),
            ]
          : null,
    );
  }

  /// Factory constructor for reports screen with filter action
  factory CustomAppBar.reports({
    Key? key,
    required BuildContext context,
    String title = 'Reports',
  }) {
    return CustomAppBar(
      key: key,
      title: title,
      centerTitle: false,
      actions: [
        IconButton(
          icon: const Icon(Icons.filter_list_outlined),
          onPressed: () {
            // Handle filter
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(content: Text('Filter options')),
            );
          },
          tooltip: 'Filter',
        ),
        IconButton(
          icon: const Icon(Icons.download_outlined),
          onPressed: () {
            // Handle export
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(content: Text('Export report')),
            );
          },
          tooltip: 'Export',
        ),
      ],
    );
  }

  /// Factory constructor for management screens
  factory CustomAppBar.management({
    Key? key,
    required BuildContext context,
    required String title,
    bool showAdd = true,
  }) {
    return CustomAppBar(
      key: key,
      title: title,
      centerTitle: false,
      actions: showAdd
          ? [
              IconButton(
                icon: const Icon(Icons.add_outlined),
                onPressed: () {
                  // Handle add new item
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(content: Text('Add new ${title.toLowerCase()}')),
                  );
                },
                tooltip: 'Add',
              ),
            ]
          : null,
    );
  }
}

/// Enum defining different app bar variants for various contexts
enum CustomAppBarVariant {
  /// Standard app bar with normal height and styling
  standard,

  /// Large app bar with increased height and font size
  large,

  /// Small app bar with reduced height and font size
  small,

  /// Transparent app bar with no background or elevation
  transparent,
}
