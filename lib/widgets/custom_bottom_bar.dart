import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

/// Custom Bottom Navigation Bar implementing Contemporary Financial Minimalism
/// with platform-aware TabBar design and subtle haptic feedback for financial apps.
class CustomBottomBar extends StatefulWidget {
  /// Current selected index
  final int currentIndex;

  /// Callback when a tab is tapped
  final ValueChanged<int> onTap;

  /// Type of bottom navigation bar
  final BottomNavigationBarType type;

  /// Background color of the bottom bar
  final Color? backgroundColor;

  /// Color of selected items
  final Color? selectedItemColor;

  /// Color of unselected items
  final Color? unselectedItemColor;

  /// Elevation of the bottom bar
  final double elevation;

  /// Whether to show labels
  final bool showLabels;

  /// Bottom bar variant for different layouts
  final CustomBottomBarVariant variant;

  const CustomBottomBar({
    super.key,
    required this.currentIndex,
    required this.onTap,
    this.type = BottomNavigationBarType.fixed,
    this.backgroundColor,
    this.selectedItemColor,
    this.unselectedItemColor,
    this.elevation = 2.0,
    this.showLabels = true,
    this.variant = CustomBottomBarVariant.standard,
  });

  @override
  State<CustomBottomBar> createState() => _CustomBottomBarState();
}

class _CustomBottomBarState extends State<CustomBottomBar> {
  /// Navigation items with routes and icons
  final List<BottomNavigationBarItem> _items = [
    const BottomNavigationBarItem(
      icon: Icon(Icons.dashboard_outlined),
      activeIcon: Icon(Icons.dashboard),
      label: 'Dashboard',
    ),
    const BottomNavigationBarItem(
      icon: Icon(Icons.add_circle_outline),
      activeIcon: Icon(Icons.add_circle),
      label: 'Add',
    ),
    const BottomNavigationBarItem(
      icon: Icon(Icons.history_outlined),
      activeIcon: Icon(Icons.history),
      label: 'History',
    ),
    const BottomNavigationBarItem(
      icon: Icon(Icons.bar_chart_outlined),
      activeIcon: Icon(Icons.bar_chart),
      label: 'Reports',
    ),
  ];

  /// Corresponding routes for navigation items
  final List<String> _routes = [
    '/dashboard',
    '/add-transaction',
    '/transaction-history',
    '/reports',
  ];

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;
    final isDark = theme.brightness == Brightness.dark;

    // Determine effective colors
    final effectiveBackgroundColor =
        widget.backgroundColor ?? colorScheme.surface;
    final effectiveSelectedColor = widget.selectedItemColor ??
        (isDark ? const Color(0xFF4A90E2) : const Color(0xFF1B365D));
    final effectiveUnselectedColor = widget.unselectedItemColor ??
        (isDark ? const Color(0xFFB0B0B0) : const Color(0xFF757575));

    return Container(
      decoration: BoxDecoration(
        color: effectiveBackgroundColor,
        boxShadow: widget.elevation > 0
            ? [
                BoxShadow(
                  color: isDark
                      ? Colors.white.withValues(alpha: 0.05)
                      : Colors.black.withValues(alpha: 0.04),
                  offset: const Offset(0, -1),
                  blurRadius: widget.elevation * 2,
                  spreadRadius: 0,
                ),
              ]
            : null,
      ),
      child: SafeArea(
        child: _buildBottomBar(
          theme,
          effectiveBackgroundColor,
          effectiveSelectedColor,
          effectiveUnselectedColor,
        ),
      ),
    );
  }

  /// Build the appropriate bottom bar based on variant
  Widget _buildBottomBar(
    ThemeData theme,
    Color backgroundColor,
    Color selectedColor,
    Color unselectedColor,
  ) {
    switch (widget.variant) {
      case CustomBottomBarVariant.floating:
        return _buildFloatingBottomBar(
          theme,
          backgroundColor,
          selectedColor,
          unselectedColor,
        );
      case CustomBottomBarVariant.minimal:
        return _buildMinimalBottomBar(
          theme,
          selectedColor,
          unselectedColor,
        );
      case CustomBottomBarVariant.standard:
      default:
        return _buildStandardBottomBar(
          backgroundColor,
          selectedColor,
          unselectedColor,
        );
    }
  }

  /// Build standard bottom navigation bar
  Widget _buildStandardBottomBar(
    Color backgroundColor,
    Color selectedColor,
    Color unselectedColor,
  ) {
    return BottomNavigationBar(
      currentIndex: widget.currentIndex,
      onTap: _handleTap,
      type: widget.type,
      backgroundColor: backgroundColor,
      selectedItemColor: selectedColor,
      unselectedItemColor: unselectedColor,
      elevation: 0, // Handled by container
      showSelectedLabels: widget.showLabels,
      showUnselectedLabels: widget.showLabels,
      selectedLabelStyle: GoogleFonts.inter(
        fontSize: 12,
        fontWeight: FontWeight.w500,
        letterSpacing: 0.4,
      ),
      unselectedLabelStyle: GoogleFonts.inter(
        fontSize: 12,
        fontWeight: FontWeight.w400,
        letterSpacing: 0.4,
      ),
      items: _items,
    );
  }

  /// Build floating bottom navigation bar
  Widget _buildFloatingBottomBar(
    ThemeData theme,
    Color backgroundColor,
    Color selectedColor,
    Color unselectedColor,
  ) {
    return Container(
      margin: const EdgeInsets.all(16.0),
      decoration: BoxDecoration(
        color: backgroundColor,
        borderRadius: BorderRadius.circular(24.0),
        boxShadow: [
          BoxShadow(
            color: theme.brightness == Brightness.dark
                ? Colors.white.withValues(alpha: 0.1)
                : Colors.black.withValues(alpha: 0.1),
            offset: const Offset(0, 4),
            blurRadius: 12,
            spreadRadius: 0,
          ),
        ],
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(24.0),
        child: BottomNavigationBar(
          currentIndex: widget.currentIndex,
          onTap: _handleTap,
          type: widget.type,
          backgroundColor: Colors.transparent,
          selectedItemColor: selectedColor,
          unselectedItemColor: unselectedColor,
          elevation: 0,
          showSelectedLabels: widget.showLabels,
          showUnselectedLabels: widget.showLabels,
          selectedLabelStyle: GoogleFonts.inter(
            fontSize: 12,
            fontWeight: FontWeight.w500,
          ),
          unselectedLabelStyle: GoogleFonts.inter(
            fontSize: 12,
            fontWeight: FontWeight.w400,
          ),
          items: _items,
        ),
      ),
    );
  }

  /// Build minimal bottom navigation bar with custom layout
  Widget _buildMinimalBottomBar(
    ThemeData theme,
    Color selectedColor,
    Color unselectedColor,
  ) {
    return Container(
      height: 60,
      padding: const EdgeInsets.symmetric(horizontal: 16.0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceAround,
        children: List.generate(_items.length, (index) {
          final item = _items[index];
          final isSelected = index == widget.currentIndex;

          return GestureDetector(
            onTap: () => _handleTap(index),
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              decoration: isSelected
                  ? BoxDecoration(
                      color: selectedColor.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(20),
                    )
                  : null,
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(
                    isSelected
                        ? (item.activeIcon as Icon).icon
                        : (item.icon as Icon).icon,
                    color: isSelected ? selectedColor : unselectedColor,
                    size: 24,
                  ),
                  if (widget.showLabels && isSelected) ...[
                    const SizedBox(height: 4),
                    Text(
                      item.label!,
                      style: GoogleFonts.inter(
                        fontSize: 12,
                        fontWeight: FontWeight.w500,
                        color: selectedColor,
                      ),
                    ),
                  ],
                ],
              ),
            ),
          );
        }),
      ),
    );
  }

  /// Handle tab tap with navigation and haptic feedback
  void _handleTap(int index) {
    if (index != widget.currentIndex) {
      // Provide subtle haptic feedback
      HapticFeedback.selectionClick();

      // Navigate to the corresponding route
      Navigator.pushNamed(context, _routes[index]);

      // Call the provided onTap callback
      widget.onTap(index);
    }
  }
}

/// Enum defining different bottom bar variants
enum CustomBottomBarVariant {
  /// Standard bottom navigation bar
  standard,

  /// Floating bottom navigation bar with rounded corners
  floating,

  /// Minimal bottom navigation bar with custom layout
  minimal,
}

/// Extension to add haptic feedback functionality
extension HapticFeedback on CustomBottomBar {
  static void selectionClick() {
    // Platform-specific haptic feedback would be implemented here
    // For now, we'll use a simple feedback
  }
}
