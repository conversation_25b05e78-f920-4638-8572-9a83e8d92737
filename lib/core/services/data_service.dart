import '../models/models.dart';
import '../repositories/repositories.dart';
import 'service_locator.dart';

class DataService {
  static DataService? _instance;
  static DataService get instance => _instance ??= DataService._();

  DataService._();

  // Repository instances
  UserRepository get _userRepo => ServiceLocator.instance.userRepository;
  BusinessRepository get _businessRepo =>
      ServiceLocator.instance.businessRepository;
  CashbookRepository get _cashbookRepo =>
      ServiceLocator.instance.cashbookRepository;
  TransactionRepository get _transactionRepo =>
      ServiceLocator.instance.transactionRepository;
  CategoryRepository get _categoryRepo =>
      ServiceLocator.instance.categoryRepository;
  PaymentModeRepository get _paymentModeRepo =>
      ServiceLocator.instance.paymentModeRepository;
  CashbookTemplateRepository get _templateRepo =>
      ServiceLocator.instance.cashbookTemplateRepository;

  // User operations
  Future<UserModel?> getCurrentUser() => _userRepo.getCurrentUser();

  Future<UserModel> updateUserProfile({
    String? fullName,
    String? avatarUrl,
    String? phone,
  }) =>
      _userRepo.updateProfile(
        fullName: fullName,
        avatarUrl: avatarUrl,
        phone: phone,
      );

  Future<void> addCashbookMeta(CashbookModel cashbook) =>
      _cashbookRepo.addCashbookMeta(cashbook);

  Future<List> getUserCashbookPermissions(String cashbookId) =>
      _cashbookRepo.getUserCashbookPermissions(cashbookId);
  // Business operations
  Future<List<BusinessModel>> getUserBusinesses() =>
      _businessRepo.getUserBusinesses();

  Future<int> countCashbookMembers(CashbookModel cashbook) =>
      _cashbookRepo.countCashbookMembers(cashbook);

  Future<List<BusinessModel>> getInvitedBusinesses() =>
      _businessRepo.getInvitedBusinesses();

  Future<BusinessModel?> getBusinessById(String id) =>
      _businessRepo.getBusinessById(id);

  Future<BusinessModel?> getDefaultBusiness() =>
      _businessRepo.getDefaultBusiness();

  Future<BusinessModel> createBusiness({
    required String name,
    String? description,
    String? logoUrl,
    String? address,
    String? phone,
    String? email,
    String? website,
    String? taxId,
    String currency = 'USD',
    bool isDefault = false,
  }) =>
      _businessRepo.createBusiness(
        name: name,
        description: description,
        logoUrl: logoUrl,
        address: address,
        phone: phone,
        email: email,
        website: website,
        taxId: taxId,
        currency: currency,
        isDefault: isDefault,
      );

  Future<BusinessModel> updateBusiness({
    required String businessId,
    String? name,
    String? description,
    String? logoUrl,
    String? address,
    String? phone,
    String? email,
    String? website,
    String? taxId,
    String? currency,
    bool? isDefault,
  }) =>
      _businessRepo.updateBusiness(
        businessId: businessId,
        name: name,
        description: description,
        logoUrl: logoUrl,
        address: address,
        phone: phone,
        email: email,
        website: website,
        taxId: taxId,
        currency: currency,
        isDefault: isDefault,
      );

  Future<void> deleteBusiness(String businessId) =>
      _businessRepo.deleteBusiness(businessId);

  // Cashbook operations
  Future<List<CashbookModel>> getUserCashbooks(String businessId) =>
      _cashbookRepo.getUserCashbooks(businessId);

  Future<CashbookModel?> getCashbookById(String id) =>
      _cashbookRepo.getCashbookById(id);

  Future<CashbookModel?> getDefaultCashbook() =>
      _cashbookRepo.getDefaultCashbook();

  Future<List<CashbookModel>> getCashbooksByBusiness(String businessId) =>
      _cashbookRepo.getCashbooksByBusiness(businessId);

  Future<CashbookModel> createCashbook({
    required String name,
    String? businessId,
    String? templateId,
    String? description,
    String currency = 'USD',
    String? color,
    String? icon,
    bool isDefault = false,
  }) =>
      _cashbookRepo.createCashbook(
        name: name,
        businessId: businessId,
        templateId: templateId,
        description: description,
        currency: currency,
        color: color,
        icon: icon,
        isDefault: isDefault,
      );

  Future<CashbookModel> updateCashbook({
    required String cashbookId,
    String? name,
    String? businessId,
    String? templateId,
    String? description,
    String? currency,
    String? color,
    String? icon,
    bool? isDefault,
    bool? isActive,
  }) =>
      _cashbookRepo.updateCashbook(
        cashbookId: cashbookId,
        name: name,
        businessId: businessId,
        templateId: templateId,
        description: description,
        currency: currency,
        color: color,
        icon: icon,
        isDefault: isDefault,
        isActive: isActive,
      );

  Future<void> deleteCashbook(String cashbookId) =>
      _cashbookRepo.deleteCashbook(cashbookId);

  // Transaction operations
  Future<List<TransactionModel>> getCashbookTransactions({
    required String cashbookId,
    int? limit,
    int? offset,
    DateTime? startDate,
    DateTime? endDate,
    TransactionType? type,
    String? categoryId,
  }) =>
      _transactionRepo.getCashbookTransactions(
        cashbookId: cashbookId,
        limit: limit,
        offset: offset,
        startDate: startDate,
        endDate: endDate,
        type: type,
        categoryId: categoryId,
      );

  Future<TransactionModel?> getTransactionById(String id) =>
      _transactionRepo.getTransactionById(id);

  Future<TransactionModel> createTransaction({
    required String cashbookId,
    required TransactionType type,
    required double amount,
    required String description,
    required DateTime transactionDate,
    String? categoryId,
    String? paymentModeId,
    String? notes,
    String? referenceNumber,
  }) =>
      _transactionRepo.createTransaction(
        cashbookId: cashbookId,
        type: type,
        amount: amount,
        description: description,
        transactionDate: transactionDate,
        categoryId: categoryId,
        paymentModeId: paymentModeId,
        notes: notes,
        referenceNumber: referenceNumber,
      );

  Future<TransactionModel> updateTransaction({
    required String transactionId,
    TransactionType? type,
    double? amount,
    String? description,
    DateTime? transactionDate,
    String? categoryId,
    String? paymentModeId,
    String? notes,
    String? referenceNumber,
  }) =>
      _transactionRepo.updateTransaction(
        transactionId: transactionId,
        type: type,
        amount: amount,
        description: description,
        transactionDate: transactionDate,
        categoryId: categoryId,
        paymentModeId: paymentModeId,
        notes: notes,
        referenceNumber: referenceNumber,
      );

  Future<void> deleteTransaction(String transactionId) =>
      _transactionRepo.deleteTransaction(transactionId);

  Future<List<TransactionModel>> searchTransactions({
    required String cashbookId,
    required String searchTerm,
    int? limit,
  }) =>
      _transactionRepo.searchTransactions(
        cashbookId: cashbookId,
        searchTerm: searchTerm,
        limit: limit,
      );

  Future<List<TransactionModel>> getRecentTransactions(
          String cashbookId, int? limit) =>
      _transactionRepo.getRecentTransactions(cashbookId, limit);

  // Category operations
  Future<List<CategoryModel>> getAllCategories() =>
      _categoryRepo.getAllCategories();

  Future<List<CategoryModel>> getCategoriesByTemplate(String templateId) =>
      _categoryRepo.getCategoriesByTemplate(templateId);

  Future<List<CategoryModel>> getIncomeCategories() =>
      _categoryRepo.getIncomeCategories();

  Future<List<CategoryModel>> getExpenseCategories() =>
      _categoryRepo.getExpenseCategories();

  Future<CategoryModel?> getCategoryById(String id) =>
      _categoryRepo.getCategoryById(id);

  Future<List<CategoryModel>> searchCategories({
    required String searchTerm,
    CategoryType? type,
    String? templateId,
  }) =>
      _categoryRepo.searchCategories(
        searchTerm: searchTerm,
        type: type,
        templateId: templateId,
      );

  // Payment mode operations
  Future<List<PaymentModeModel>> getAllPaymentModes() =>
      _paymentModeRepo.getAllPaymentModes();

  Future<PaymentModeModel?> getPaymentModeById(String id) =>
      _paymentModeRepo.getPaymentModeById(id);

  Future<List<PaymentModeModel>> searchPaymentModes(String searchTerm) =>
      _paymentModeRepo.searchPaymentModes(searchTerm);

  // Template operations
  Future<List<CashbookTemplateModel>> getAllTemplates() =>
      _templateRepo.getAllTemplates();

  Future<CashbookTemplateModel?> getTemplateById(String id) =>
      _templateRepo.getTemplateById(id);

  Future<List<CashbookTemplateModel>> searchTemplates(String searchTerm) =>
      _templateRepo.searchTemplates(searchTerm);

  Future<Map<String, dynamic>> getTemplateWithCategories(String templateId) =>
      _templateRepo.getTemplateWithCategories(templateId);

  // Dashboard data
  Future<Map<String, dynamic>> getDashboardData(String cashbookId) async {
    try {
      CashbookModel? cashbook = await getCashbookById(cashbookId);

      final futures = await Future.wait([
        getCashbooksByBusiness(cashbook!.businessId!),
        getRecentTransactions(cashbookId, 10),
      ]);

      final cashbooks = futures[0] as List<CashbookModel>;
      final recentTransactions = futures[1] as List<TransactionModel>;
      final defaultCashbook = cashbook;

      // Calculate totals
      double totalBalance = 0;
      int totalTransactions = 0;

      for (final cashbook in cashbooks) {
        totalBalance += cashbook.balance;
        totalTransactions += cashbook.transactionCount;
      }
      return {
        'cashbooks': cashbooks,
        'recent_transactions': recentTransactions,
        'default_cashbook': defaultCashbook,
        'total_balance': totalBalance,
        'total_transactions': totalTransactions,
        'active_cashbooks': cashbooks.length,
      };
    } catch (error) {
      throw Exception('Failed to load dashboard data: $error');
    }
  }

  // Setup initial data for new user
  Future<void> setupInitialData({
    String? businessName,
    String? cashbookName,
    String? templateId,
  }) async {
    try {
      BusinessModel? business;

      // Create business if provided
      if (businessName != null && businessName.isNotEmpty) {
        business = await createBusiness(
          name: businessName,
          isDefault: true,
        );
      }
    } catch (error) {
      throw Exception('Failed to setup initial data: $error');
    }
  }
}
