import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';

/// Service for managing application preferences and settings
class PreferencesService {
  static const String _themeKey = 'theme_mode';
  static const String _languageKey = 'language_code';
  static const String _notificationsKey = 'notifications_enabled';
  static const String _biometricKey = 'biometric_enabled';
  static const String _autoBackupKey = 'auto_backup_enabled';
  static const String _currencyKey = 'default_currency';
  static const String _dateFormatKey = 'date_format';
  static const String _firstLaunchKey = 'first_launch';

  static SharedPreferences? _prefs;

  /// Initialize the preferences service
  static Future<void> init() async {
    _prefs = await SharedPreferences.getInstance();
  }

  static Future<void> setActiveBusiness(String businessId) async {
    print('setting active business: $businessId');
    prefs.setString('active_business', businessId);
  }

  static String? getActiveBusiness() {
    return prefs.getString('active_business');
  }

  /// Get SharedPreferences instance
  static SharedPreferences get prefs {
    if (_prefs == null) {
      throw Exception('PreferencesService not initialized. Call init() first.');
    }
    return _prefs!;
  }

  // Theme Settings
  static ThemeMode getThemeMode() {
    final themeIndex = prefs.getInt(_themeKey) ?? 0;
    return ThemeMode.values[themeIndex];
  }

  static Future<bool> setThemeMode(ThemeMode themeMode) {
    return prefs.setInt(_themeKey, themeMode.index);
  }

  // Language Settings
  static String getLanguageCode() {
    return prefs.getString(_languageKey) ?? 'en';
  }

  static Future<bool> setLanguageCode(String languageCode) {
    return prefs.setString(_languageKey, languageCode);
  }

  // Notification Settings
  static bool getNotificationsEnabled() {
    return prefs.getBool(_notificationsKey) ?? true;
  }

  static Future<bool> setNotificationsEnabled(bool enabled) {
    return prefs.setBool(_notificationsKey, enabled);
  }

  // Biometric Settings
  static bool getBiometricEnabled() {
    return prefs.getBool(_biometricKey) ?? false;
  }

  static Future<bool> setBiometricEnabled(bool enabled) {
    return prefs.setBool(_biometricKey, enabled);
  }

  // Auto Backup Settings
  static bool getAutoBackupEnabled() {
    return prefs.getBool(_autoBackupKey) ?? true;
  }

  static Future<bool> setAutoBackupEnabled(bool enabled) {
    return prefs.setBool(_autoBackupKey, enabled);
  }

  // Currency Settings
  static String getDefaultCurrency() {
    return prefs.getString(_currencyKey) ?? 'USD';
  }

  static Future<bool> setDefaultCurrency(String currency) {
    return prefs.setString(_currencyKey, currency);
  }

  // Date Format Settings
  static String getDateFormat() {
    return prefs.getString(_dateFormatKey) ?? 'MM/dd/yyyy';
  }

  static Future<bool> setDateFormat(String format) {
    return prefs.setString(_dateFormatKey, format);
  }

  // First Launch
  static bool isFirstLaunch() {
    return prefs.getBool(_firstLaunchKey) ?? true;
  }

  static Future<bool> setFirstLaunch(bool isFirst) {
    return prefs.setBool(_firstLaunchKey, isFirst);
  }

  // Clear all preferences
  static Future<bool> clearAll() {
    return prefs.clear();
  }

  // Export settings as Map
  static Map<String, dynamic> exportSettings() {
    return {
      'theme_mode': getThemeMode().index,
      'language_code': getLanguageCode(),
      'notifications_enabled': getNotificationsEnabled(),
      'biometric_enabled': getBiometricEnabled(),
      'auto_backup_enabled': getAutoBackupEnabled(),
      'default_currency': getDefaultCurrency(),
      'date_format': getDateFormat(),
    };
  }

  // Import settings from Map
  static Future<void> importSettings(Map<String, dynamic> settings) async {
    if (settings.containsKey('theme_mode')) {
      await setThemeMode(ThemeMode.values[settings['theme_mode']]);
    }
    if (settings.containsKey('language_code')) {
      await setLanguageCode(settings['language_code']);
    }
    if (settings.containsKey('notifications_enabled')) {
      await setNotificationsEnabled(settings['notifications_enabled']);
    }
    if (settings.containsKey('biometric_enabled')) {
      await setBiometricEnabled(settings['biometric_enabled']);
    }
    if (settings.containsKey('auto_backup_enabled')) {
      await setAutoBackupEnabled(settings['auto_backup_enabled']);
    }
    if (settings.containsKey('default_currency')) {
      await setDefaultCurrency(settings['default_currency']);
    }
    if (settings.containsKey('date_format')) {
      await setDateFormat(settings['date_format']);
    }
  }
}
