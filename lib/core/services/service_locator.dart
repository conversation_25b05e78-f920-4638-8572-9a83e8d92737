import 'package:provider/provider.dart';
import 'package:provider/single_child_widget.dart';

import '../repositories/repositories.dart';
import 'supabase_service.dart';

class ServiceLocator {
  static ServiceLocator? _instance;
  static ServiceLocator get instance => _instance ??= ServiceLocator._();
  
  ServiceLocator._();

  // Repositories
  late final UserRepository _userRepository;
  late final BusinessRepository _businessRepository;
  late final CashbookRepository _cashbookRepository;
  late final TransactionRepository _transactionRepository;
  late final CategoryRepository _categoryRepository;
  late final PaymentModeRepository _paymentModeRepository;
  late final CashbookTemplateRepository _cashbookTemplateRepository;

  // Getters for repositories
  UserRepository get userRepository => _userRepository;
  BusinessRepository get businessRepository => _businessRepository;
  CashbookRepository get cashbookRepository => _cashbookRepository;
  TransactionRepository get transactionRepository => _transactionRepository;
  CategoryRepository get categoryRepository => _categoryRepository;
  PaymentModeRepository get paymentModeRepository => _paymentModeRepository;
  CashbookTemplateRepository get cashbookTemplateRepository => _cashbookTemplateRepository;

  /// Initialize all services and repositories
  void initialize() {
    _userRepository = UserRepository();
    _businessRepository = BusinessRepository();
    _cashbookRepository = CashbookRepository();
    _transactionRepository = TransactionRepository();
    _categoryRepository = CategoryRepository();
    _paymentModeRepository = PaymentModeRepository();
    _cashbookTemplateRepository = CashbookTemplateRepository();
  }

  /// Get providers for dependency injection
  static List<SingleChildWidget> getProviders() {
    return [
      // Services
      Provider<SupabaseService>.value(value: SupabaseService.instance),
      
      // Repositories
      Provider<UserRepository>.value(value: ServiceLocator.instance.userRepository),
      Provider<BusinessRepository>.value(value: ServiceLocator.instance.businessRepository),
      Provider<CashbookRepository>.value(value: ServiceLocator.instance.cashbookRepository),
      Provider<TransactionRepository>.value(value: ServiceLocator.instance.transactionRepository),
      Provider<CategoryRepository>.value(value: ServiceLocator.instance.categoryRepository),
      Provider<PaymentModeRepository>.value(value: ServiceLocator.instance.paymentModeRepository),
      Provider<CashbookTemplateRepository>.value(value: ServiceLocator.instance.cashbookTemplateRepository),
    ];
  }

  /// Dispose all resources
  void dispose() {
    // Add any cleanup logic here if needed
  }
}