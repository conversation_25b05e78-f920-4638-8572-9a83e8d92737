import 'dart:io';
import 'package:path_provider/path_provider.dart';
import 'package:csv/csv.dart';
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;
import 'package:share_plus/share_plus.dart';

import '../models/models.dart';
import '../repositories/transaction_repository.dart';
import '../repositories/cashbook_repository.dart';
import '../repositories/category_repository.dart';
import 'auth_service.dart';

class ReportsService {
  static ReportsService? _instance;
  static ReportsService get instance => _instance ??= ReportsService._();

  ReportsService._();

  final TransactionRepository _transactionRepository = TransactionRepository();
  final CashbookRepository _cashbookRepository = CashbookRepository();
  final CategoryRepository _categoryRepository = CategoryRepository();
  final AuthService _authService = AuthService.instance;

  /// Get financial summary for a date range
  Future<Map<String, dynamic>> getFinancialSummary({
    String? cashbookId,
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    final transactions = await _getTransactionsForPeriod(
      cashbookId: cashbookId,
      startDate: startDate,
      endDate: endDate,
    );

    final income = transactions
        .where((t) => t.type == TransactionType.income)
        .fold(0.0, (sum, t) => sum + t.amount);

    final expenses = transactions
        .where((t) => t.type == TransactionType.expense)
        .fold(0.0, (sum, t) => sum + t.amount);

    final netProfit = income - expenses;

    // Calculate previous period for comparison
    final periodDays =
        endDate?.difference(startDate ?? DateTime.now()).inDays ?? 30;
    final previousStartDate =
        (startDate ?? DateTime.now()).subtract(Duration(days: periodDays));
    final previousEndDate = startDate ?? DateTime.now();

    final previousTransactions = await _getTransactionsForPeriod(
      cashbookId: cashbookId,
      startDate: previousStartDate,
      endDate: previousEndDate,
    );

    final previousIncome = previousTransactions
        .where((t) => t.type == TransactionType.income)
        .fold(0.0, (sum, t) => sum + t.amount);

    final previousExpenses = previousTransactions
        .where((t) => t.type == TransactionType.expense)
        .fold(0.0, (sum, t) => sum + t.amount);

    final previousNetProfit = previousIncome - previousExpenses;

    return {
      'totalIncome': income,
      'totalExpenses': expenses,
      'netProfit': netProfit,
      'incomeChange': previousIncome > 0
          ? ((income - previousIncome) / previousIncome) * 100
          : 0,
      'expensesChange': previousExpenses > 0
          ? ((expenses - previousExpenses) / previousExpenses) * 100
          : 0,
      'profitChange': previousNetProfit != 0
          ? ((netProfit - previousNetProfit) / previousNetProfit.abs()) * 100
          : 0,
      'transactionCount': transactions.length,
    };
  }

  /// Get category analysis
  Future<List<Map<String, dynamic>>> getCategoryAnalysis({
    String? cashbookId,
    DateTime? startDate,
    DateTime? endDate,
    TransactionType? type,
  }) async {
    final transactions = await _getTransactionsForPeriod(
      cashbookId: cashbookId,
      startDate: startDate,
      endDate: endDate,
    );

    final filteredTransactions = type != null
        ? transactions.where((t) => t.type == type).toList()
        : transactions;

    final categories = await _categoryRepository.getAllCategories();
    final categoryMap = {for (var cat in categories) cat.id: cat};

    final categoryTotals = <String, double>{};
    for (final transaction in filteredTransactions) {
      final categoryId = transaction.categoryId ?? 'uncategorized';
      categoryTotals[categoryId] =
          (categoryTotals[categoryId] ?? 0) + transaction.amount;
    }

    final totalAmount =
        categoryTotals.values.fold(0.0, (sum, amount) => sum + amount);

    final result = categoryTotals.entries.map((entry) {
      final category = categoryMap[entry.key];
      final percentage =
          totalAmount > 0 ? (entry.value / totalAmount) * 100 : 0;

      return {
        'categoryId': entry.key,
        'categoryName': category?.name ?? 'Unknown',
        'categoryIcon': category?.icon ?? 'category',
        'amount': entry.value,
        'percentage': percentage,
        'transactionCount': filteredTransactions
            .where((t) => (t.categoryId ?? 'uncategorized') == entry.key)
            .length,
      };
    }).toList();

    result.sort(
        (a, b) => (b['amount'] as double).compareTo(a['amount'] as double));
    return result;
  }

  /// Get monthly trend data
  Future<List<Map<String, dynamic>>> getMonthlyTrends({
    String? cashbookId,
    int months = 6,
  }) async {
    final endDate = DateTime.now();
    final startDate = DateTime(endDate.year, endDate.month - months, 1);

    final transactions = await _getTransactionsForPeriod(
      cashbookId: cashbookId,
      startDate: startDate,
      endDate: endDate,
    );

    final monthlyData = <String, Map<String, double>>{};

    for (final transaction in transactions) {
      final monthKey =
          '${transaction.transactionDate.year}-${transaction.transactionDate.month.toString().padLeft(2, '0')}';

      if (!monthlyData.containsKey(monthKey)) {
        monthlyData[monthKey] = {'income': 0.0, 'expense': 0.0};
      }

      if (transaction.type == TransactionType.income) {
        monthlyData[monthKey]!['income'] =
            monthlyData[monthKey]!['income']! + transaction.amount;
      } else {
        monthlyData[monthKey]!['expense'] =
            monthlyData[monthKey]!['expense']! + transaction.amount;
      }
    }

    final result = <Map<String, dynamic>>[];
    for (int i = 0; i < months; i++) {
      final date = DateTime(endDate.year, endDate.month - i, 1);
      final monthKey = '${date.year}-${date.month.toString().padLeft(2, '0')}';
      final data = monthlyData[monthKey] ?? {'income': 0.0, 'expense': 0.0};

      result.insert(0, {
        'month': _getMonthName(date.month),
        'year': date.year,
        'income': data['income'],
        'expense': data['expense'],
        'net': data['income']! - data['expense']!,
      });
    }

    return result;
  }

  /// Get payment mode analysis
  Future<List<Map<String, dynamic>>> getPaymentModeAnalysis({
    String? cashbookId,
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    final transactions = await _getTransactionsForPeriod(
      cashbookId: cashbookId,
      startDate: startDate,
      endDate: endDate,
    );

    final paymentModeTotals = <String, double>{};
    for (final transaction in transactions) {
      final paymentMode = transaction.paymentModeId ?? 'cash';
      paymentModeTotals[paymentMode] =
          (paymentModeTotals[paymentMode] ?? 0) + transaction.amount;
    }

    final totalAmount =
        paymentModeTotals.values.fold(0.0, (sum, amount) => sum + amount);

    return paymentModeTotals.entries.map((entry) {
      final percentage =
          totalAmount > 0 ? (entry.value / totalAmount) * 100 : 0;

      return {
        'paymentMode': entry.key,
        'amount': entry.value,
        'percentage': percentage,
        'transactionCount': transactions
            .where((t) => (t.paymentModeId ?? 'cash') == entry.key)
            .length,
      };
    }).toList()
      ..sort(
          (a, b) => (b['amount'] as double).compareTo(a['amount'] as double));
  }

  /// Export data to CSV
  Future<String> exportToCSV({
    String? cashbookId,
    DateTime? startDate,
    DateTime? endDate,
    String reportType = 'transactions',
  }) async {
    List<List<dynamic>> csvData = [];

    switch (reportType) {
      case 'transactions':
        csvData = await _generateTransactionCSV(cashbookId, startDate, endDate);
        break;
      case 'summary':
        csvData = await _generateSummaryCSV(cashbookId, startDate, endDate);
        break;
      case 'categories':
        csvData = await _generateCategoryCSV(cashbookId, startDate, endDate);
        break;
    }

    final csv = const ListToCsvConverter().convert(csvData);

    final directory = await getApplicationDocumentsDirectory();
    final fileName =
        'cashbook_${reportType}_${DateTime.now().millisecondsSinceEpoch}.csv';
    final file = File('${directory.path}/$fileName');

    await file.writeAsString(csv);
    return file.path;
  }

  /// Export data to PDF
  Future<String> exportToPDF({
    String? cashbookId,
    DateTime? startDate,
    DateTime? endDate,
    String reportType = 'summary',
  }) async {
    final pdf = pw.Document();

    switch (reportType) {
      case 'summary':
        await _generateSummaryPDF(pdf, cashbookId, startDate, endDate);
        break;
      case 'transactions':
        await _generateTransactionPDF(pdf, cashbookId, startDate, endDate);
        break;
    }

    final directory = await getApplicationDocumentsDirectory();
    final fileName =
        'cashbook_${reportType}_${DateTime.now().millisecondsSinceEpoch}.pdf';
    final file = File('${directory.path}/$fileName');

    await file.writeAsBytes(await pdf.save());
    return file.path;
  }

  /// Share exported file
  Future<void> shareFile(String filePath, String title) async {
    await Share.shareXFiles([XFile(filePath)], text: title);
  }

  // Private helper methods
  Future<List<TransactionModel>> _getTransactionsForPeriod({
    String? cashbookId,
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    if (cashbookId != null) {
      return await _transactionRepository.getCashbookTransactions(
        cashbookId: cashbookId,
        startDate: startDate,
        endDate: endDate,
      );
    } else {
      return await _transactionRepository.getUserTransactions(
        startDate: startDate,
        endDate: endDate,
      );
    }
  }

  Future<List<List<dynamic>>> _generateTransactionCSV(
    String? cashbookId,
    DateTime? startDate,
    DateTime? endDate,
  ) async {
    final transactions = await _getTransactionsForPeriod(
      cashbookId: cashbookId,
      startDate: startDate,
      endDate: endDate,
    );

    final categories = await _categoryRepository.getAllCategories();
    final categoryMap = {for (var cat in categories) cat.id: cat.name};

    final csvData = <List<dynamic>>[
      ['Date', 'Description', 'Category', 'Type', 'Amount', 'Payment Mode']
    ];

    for (final transaction in transactions) {
      csvData.add([
        transaction.transactionDate.toIso8601String().split('T')[0],
        transaction.description,
        categoryMap[transaction.categoryId] ?? 'Unknown',
        transaction.type.name,
        transaction.amount,
        transaction.paymentModeId ?? 'cash',
      ]);
    }

    return csvData;
  }

  Future<List<List<dynamic>>> _generateSummaryCSV(
    String? cashbookId,
    DateTime? startDate,
    DateTime? endDate,
  ) async {
    final summary = await getFinancialSummary(
      cashbookId: cashbookId,
      startDate: startDate,
      endDate: endDate,
    );

    return [
      ['Metric', 'Value'],
      ['Total Income', summary['totalIncome']],
      ['Total Expenses', summary['totalExpenses']],
      ['Net Profit', summary['netProfit']],
      ['Transaction Count', summary['transactionCount']],
    ];
  }

  Future<List<List<dynamic>>> _generateCategoryCSV(
    String? cashbookId,
    DateTime? startDate,
    DateTime? endDate,
  ) async {
    final categoryAnalysis = await getCategoryAnalysis(
      cashbookId: cashbookId,
      startDate: startDate,
      endDate: endDate,
    );

    final csvData = <List<dynamic>>[
      ['Category', 'Amount', 'Percentage', 'Transaction Count']
    ];

    for (final category in categoryAnalysis) {
      csvData.add([
        category['categoryName'],
        category['amount'],
        category['percentage'],
        category['transactionCount'],
      ]);
    }

    return csvData;
  }

  Future<void> _generateSummaryPDF(
    pw.Document pdf,
    String? cashbookId,
    DateTime? startDate,
    DateTime? endDate,
  ) async {
    final summary = await getFinancialSummary(
      cashbookId: cashbookId,
      startDate: startDate,
      endDate: endDate,
    );

    pdf.addPage(
      pw.Page(
        build: (pw.Context context) {
          return pw.Column(
            crossAxisAlignment: pw.CrossAxisAlignment.start,
            children: [
              pw.Text('Financial Summary Report',
                  style: pw.TextStyle(
                      fontSize: 24, fontWeight: pw.FontWeight.bold)),
              pw.SizedBox(height: 20),
              pw.Text(
                  'Period: ${startDate?.toIso8601String().split('T')[0] ?? 'All time'} to ${endDate?.toIso8601String().split('T')[0] ?? 'Present'}'),
              pw.SizedBox(height: 20),
              pw.Table(
                border: pw.TableBorder.all(),
                children: [
                  pw.TableRow(children: [
                    pw.Padding(
                        padding: const pw.EdgeInsets.all(8),
                        child: pw.Text('Metric',
                            style:
                                pw.TextStyle(fontWeight: pw.FontWeight.bold))),
                    pw.Padding(
                        padding: const pw.EdgeInsets.all(8),
                        child: pw.Text('Value',
                            style:
                                pw.TextStyle(fontWeight: pw.FontWeight.bold))),
                  ]),
                  pw.TableRow(children: [
                    pw.Padding(
                        padding: const pw.EdgeInsets.all(8),
                        child: pw.Text('Total Income')),
                    pw.Padding(
                        padding: const pw.EdgeInsets.all(8),
                        child: pw.Text(
                            '\$${summary['totalIncome'].toStringAsFixed(2)}')),
                  ]),
                  pw.TableRow(children: [
                    pw.Padding(
                        padding: const pw.EdgeInsets.all(8),
                        child: pw.Text('Total Expenses')),
                    pw.Padding(
                        padding: const pw.EdgeInsets.all(8),
                        child: pw.Text(
                            '\$${summary['totalExpenses'].toStringAsFixed(2)}')),
                  ]),
                  pw.TableRow(children: [
                    pw.Padding(
                        padding: const pw.EdgeInsets.all(8),
                        child: pw.Text('Net Profit')),
                    pw.Padding(
                        padding: const pw.EdgeInsets.all(8),
                        child: pw.Text(
                            '\$${summary['netProfit'].toStringAsFixed(2)}')),
                  ]),
                  pw.TableRow(children: [
                    pw.Padding(
                        padding: const pw.EdgeInsets.all(8),
                        child: pw.Text('Transaction Count')),
                    pw.Padding(
                        padding: const pw.EdgeInsets.all(8),
                        child: pw.Text('${summary['transactionCount']}')),
                  ]),
                ],
              ),
            ],
          );
        },
      ),
    );
  }

  Future<void> _generateTransactionPDF(
    pw.Document pdf,
    String? cashbookId,
    DateTime? startDate,
    DateTime? endDate,
  ) async {
    final transactions = await _getTransactionsForPeriod(
      cashbookId: cashbookId,
      startDate: startDate,
      endDate: endDate,
    );

    final categories = await _categoryRepository.getAllCategories();
    final categoryMap = {for (var cat in categories) cat.id: cat.name};

    pdf.addPage(
      pw.MultiPage(
        build: (pw.Context context) {
          return [
            pw.Text('Transaction Report',
                style:
                    pw.TextStyle(fontSize: 24, fontWeight: pw.FontWeight.bold)),
            pw.SizedBox(height: 20),
            pw.Text(
                'Period: ${startDate?.toIso8601String().split('T')[0] ?? 'All time'} to ${endDate?.toIso8601String().split('T')[0] ?? 'Present'}'),
            pw.SizedBox(height: 20),
            pw.Table(
              border: pw.TableBorder.all(),
              columnWidths: {
                0: const pw.FixedColumnWidth(80),
                1: const pw.FlexColumnWidth(2),
                2: const pw.FlexColumnWidth(1),
                3: const pw.FixedColumnWidth(60),
                4: const pw.FixedColumnWidth(80),
              },
              children: [
                pw.TableRow(
                  decoration: const pw.BoxDecoration(color: PdfColors.grey300),
                  children: [
                    pw.Padding(
                        padding: const pw.EdgeInsets.all(4),
                        child: pw.Text('Date',
                            style:
                                pw.TextStyle(fontWeight: pw.FontWeight.bold))),
                    pw.Padding(
                        padding: const pw.EdgeInsets.all(4),
                        child: pw.Text('Description',
                            style:
                                pw.TextStyle(fontWeight: pw.FontWeight.bold))),
                    pw.Padding(
                        padding: const pw.EdgeInsets.all(4),
                        child: pw.Text('Category',
                            style:
                                pw.TextStyle(fontWeight: pw.FontWeight.bold))),
                    pw.Padding(
                        padding: const pw.EdgeInsets.all(4),
                        child: pw.Text('Type',
                            style:
                                pw.TextStyle(fontWeight: pw.FontWeight.bold))),
                    pw.Padding(
                        padding: const pw.EdgeInsets.all(4),
                        child: pw.Text('Amount',
                            style:
                                pw.TextStyle(fontWeight: pw.FontWeight.bold))),
                  ],
                ),
                ...transactions.map((transaction) => pw.TableRow(children: [
                      pw.Padding(
                          padding: const pw.EdgeInsets.all(4),
                          child: pw.Text(transaction.transactionDate
                              .toIso8601String()
                              .split('T')[0])),
                      pw.Padding(
                          padding: const pw.EdgeInsets.all(4),
                          child: pw.Text(transaction.description)),
                      pw.Padding(
                          padding: const pw.EdgeInsets.all(4),
                          child: pw.Text(categoryMap[transaction.categoryId] ??
                              'Unknown')),
                      pw.Padding(
                          padding: const pw.EdgeInsets.all(4),
                          child: pw.Text(transaction.type.name)),
                      pw.Padding(
                          padding: const pw.EdgeInsets.all(4),
                          child: pw.Text(
                              '\$${transaction.amount.toStringAsFixed(2)}')),
                    ])),
              ],
            ),
          ];
        },
      ),
    );
  }

  String _getMonthName(int month) {
    const months = [
      'Jan',
      'Feb',
      'Mar',
      'Apr',
      'May',
      'Jun',
      'Jul',
      'Aug',
      'Sep',
      'Oct',
      'Nov',
      'Dec'
    ];
    return months[month - 1];
  }
}
