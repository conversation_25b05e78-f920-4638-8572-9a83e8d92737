import 'dart:convert';
import 'package:flutter/services.dart';
import 'supabase_service.dart';
import 'service_locator.dart';

class AppConfigService {
  static AppConfigService? _instance;
  static AppConfigService get instance => _instance ??= AppConfigService._();
  
  AppConfigService._();

  Map<String, dynamic>? _config;
  bool _isInitialized = false;

  bool get isInitialized => _isInitialized;
  Map<String, dynamic>? get config => _config;

  /// Initialize the app configuration
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      // Load configuration from env.json
      await _loadConfig();
      
      // Initialize Supabase
      await _initializeSupabase();
      
      // Initialize service locator
      ServiceLocator.instance.initialize();
      
      _isInitialized = true;
    } catch (error) {
      throw Exception('Failed to initialize app configuration: $error');
    }
  }

  /// Load configuration from env.json
  Future<void> _loadConfig() async {
    try {
      final configString = await rootBundle.loadString('env.json');
      _config = json.decode(configString) as Map<String, dynamic>;
    } catch (error) {
      throw Exception('Failed to load configuration file: $error');
    }
  }

  /// Initialize Supabase with configuration
  Future<void> _initializeSupabase() async {
    if (_config == null) {
      throw Exception('Configuration not loaded');
    }

    final supabaseUrl = _config!['SUPABASE_URL'] as String?;
    final supabaseAnonKey = _config!['SUPABASE_ANON_KEY'] as String?;

    if (supabaseUrl == null || supabaseAnonKey == null) {
      throw Exception('Supabase configuration missing in env.json');
    }

    await SupabaseService.initialize(
      url: supabaseUrl,
      anonKey: supabaseAnonKey,
    );
  }

  /// Get configuration value
  T? getConfigValue<T>(String key) {
    if (_config == null) return null;
    return _config![key] as T?;
  }

  /// Get Supabase URL
  String? get supabaseUrl => getConfigValue<String>('SUPABASE_URL');

  /// Get Supabase Anon Key
  String? get supabaseAnonKey => getConfigValue<String>('SUPABASE_ANON_KEY');

  /// Get app version
  String? get appVersion => getConfigValue<String>('APP_VERSION');

  /// Get environment
  String get environment => getConfigValue<String>('ENVIRONMENT') ?? 'development';

  /// Check if in development mode
  bool get isDevelopment => environment == 'development';

  /// Check if in production mode
  bool get isProduction => environment == 'production';

  /// Get API base URL (if using external APIs)
  String? get apiBaseUrl => getConfigValue<String>('API_BASE_URL');

  /// Get storage bucket name
  String? get storageBucket => getConfigValue<String>('STORAGE_BUCKET');

  /// Reset configuration (for testing)
  void reset() {
    _config = null;
    _isInitialized = false;
  }
}