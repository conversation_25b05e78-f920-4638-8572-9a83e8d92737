import '../models/models.dart';
import '../repositories/business_member_repository.dart';
import '../repositories/business_repository.dart';
import '../repositories/user_repository.dart';
import 'auth_service.dart';

class BusinessMemberService {
  static BusinessMemberService? _instance;
  static BusinessMemberService get instance =>
      _instance ??= BusinessMemberService._();

  BusinessMemberService._();

  final BusinessMemberRepository _memberRepository = BusinessMemberRepository();
  final BusinessRepository _businessRepository = BusinessRepository();
  final UserRepository _userRepository = UserRepository();
  final AuthService _authService = AuthService.instance;

  /// Get all members for a business
  Future<List<BusinessMemberModel>> getBusinessMembers(
      String businessId) async {
    return await _memberRepository.getBusinessMembers(businessId);
  }

  /// Get current user's membership in a business
  Future<BusinessMemberModel?> getCurrentUserMembership(
      String businessId) async {
    if (!_authService.isAuthenticated) return null;

    return await _memberRepository.getMemberByUserAndBusiness(
      _authService.currentUserId!,
      businessId,
    );
  }

  /// Check if current user can manage members for a business
  Future<bool> canManageMembers(String businessId) async {
    final membership = await getCurrentUserMembership(businessId);
    if (membership == null) return false;

    // Partners and admins can manage members
    return membership.role == MemberRole.partner ||
        membership.role == MemberRole.admin;
  }

  /// Check if current user can manage cashbook permissions
  Future<bool> canManageCashbookPermissions(String businessId) async {
    final membership = await getCurrentUserMembership(businessId);
    if (membership == null) return false;

    // Partners and admins can manage permissions
    return membership.role == MemberRole.partner ||
        membership.role == MemberRole.admin;
  }

  /// Check if a user is the business creator/owner
  Future<bool> isBusinessCreator(String businessId, String userId) async {
    final business = await _businessRepository.getBusinessById(businessId);
    return business?.userId == userId;
  }

  /// Check if current user is the business creator
  Future<bool> isCurrentUserBusinessCreator(String businessId) async {
    if (!_authService.isAuthenticated) return false;
    return await isBusinessCreator(businessId, _authService.currentUserId!);
  }

  /// Check if a member can be deleted (business creator cannot be deleted)
  Future<bool> canDeleteMember(BusinessMemberModel member) async {
    return !await isBusinessCreator(member.businessId, member.userId);
  }

  /// Check if a member's role can be changed
  Future<bool> canChangeRole(
      BusinessMemberModel member, MemberRole newRole) async {
    // Business creator cannot have their role changed
    if (await isBusinessCreator(member.businessId, member.userId)) {
      return false;
    }

    // Additional business logic can be added here
    // For example, preventing demotion of the last admin
    return true;
  }

  /// Send invitation to join business
  Future<BusinessInvitationModel> sendInvitation({
    required String businessId,
    required String email,
    required MemberRole role,
  }) async {
    // Validate email format
    if (!_isValidEmail(email)) {
      throw Exception('Please enter a valid email address');
    }

    // Check if user can manage members
    if (!await canManageMembers(businessId)) {
      throw Exception('You do not have permission to invite members');
    }

    // TODO: implement this check properly in the repository
    // Check if user is already a member
    // final existingMember = await _memberRepository.getMemberByUserAndBusiness(
    //   email, // This would need to be modified to search by email
    //   businessId,
    // );

    // if (existingMember != null) {
    //   throw Exception('This user is already a member of the business');
    // }

    return await _memberRepository.sendInvitation(
      businessId: businessId,
      email: email,
      role: role,
    );
  }

  /// Get invitations for a business
  Future<List<BusinessInvitationModel>> getBusinessInvitations(
      String businessId) async {
    return await _memberRepository.getBusinessInvitations(businessId);
  }

  /// Accept invitation using token
  Future<BusinessMemberModel> acceptInvitation(String token) async {
    if (!_authService.isAuthenticated) {
      throw Exception('You must be logged in to accept an invitation');
    }

    final invitation = await _memberRepository.getInvitationByToken(token);
    if (invitation == null) {
      throw Exception('Invalid invitation token');
    }

    if (!invitation.canBeAccepted) {
      throw Exception('This invitation has expired or is no longer valid');
    }

    // Check if the invitation email matches current user's email
    final currentUser = await _userRepository.getCurrentUser();
    if (currentUser?.email != invitation.email) {
      throw Exception('This invitation was sent to a different email address');
    }

    return await _memberRepository.acceptInvitation(invitation.id);
  }

  /// Decline invitation
  Future<void> declineInvitation(String invitationId) async {
    await _memberRepository.declineInvitation(invitationId);
  }

  /// Cancel invitation (by business owner/admin)
  Future<void> cancelInvitation(String invitationId, String businessId) async {
    if (!await canManageMembers(businessId)) {
      throw Exception('You do not have permission to cancel invitations');
    }

    await _memberRepository.cancelInvitation(invitationId);
  }

  /// Update member role
  Future<BusinessMemberModel> updateMemberRole(
    String memberId,
    MemberRole newRole,
    String businessId,
  ) async {
    if (!await canManageMembers(businessId)) {
      throw Exception('You do not have permission to update member roles');
    }

    // Get the member to check if role can be changed
    final members = await getBusinessMembers(businessId);
    final member = members.firstWhere((m) => m.id == memberId);

    if (!await canChangeRole(member, newRole)) {
      throw Exception('Cannot change role for business creator');
    }

    return await _memberRepository.updateMemberRole(memberId, newRole);
  }

  /// Remove member from business
  Future<void> removeMember(String memberId, String businessId) async {
    if (!await canManageMembers(businessId)) {
      throw Exception('You do not have permission to remove members');
    }

    // Get the member to check if they can be deleted
    final members = await getBusinessMembers(businessId);
    final member = members.firstWhere((m) => m.id == memberId);

    if (!await canDeleteMember(member)) {
      throw Exception('Cannot remove business creator from the business');
    }

    await _memberRepository.removeMember(memberId);
  }

  /// Grant cashbook permission to member
  Future<MemberCashbookPermissionModel> grantCashbookPermission({
    required String businessMemberId,
    required String cashbookId,
    required CashbookPermission permission,
    required String businessId,
  }) async {
    if (!await canManageCashbookPermissions(businessId)) {
      throw Exception(
          'You do not have permission to manage cashbook permissions');
    }

    return await _memberRepository.grantCashbookPermission(
      businessMemberId: businessMemberId,
      cashbookId: cashbookId,
      permission: permission,
    );
  }

  /// Get member's cashbook permissions
  Future<List<MemberCashbookPermissionModel>> getMemberCashbookPermissions(
    String businessMemberId,
  ) async {
    return await _memberRepository
        .getMemberCashbookPermissions(businessMemberId);
  }

  /// Get all permissions for a cashbook
  Future<List<MemberCashbookPermissionModel>> getCashbookPermissions(
    String cashbookId,
  ) async {
    return await _memberRepository.getCashbookPermissions(cashbookId);
  }

  /// Revoke cashbook permission
  Future<void> revokeCashbookPermission(
    String permissionId,
    String businessId,
  ) async {
    if (!await canManageCashbookPermissions(businessId)) {
      throw Exception(
          'You do not have permission to manage cashbook permissions');
    }

    await _memberRepository.revokeCashbookPermission(permissionId);
  }

  /// Update cashbook permission
  Future<MemberCashbookPermissionModel> updateCashbookPermission(
    String permissionId,
    CashbookPermission newPermission,
    String businessId,
  ) async {
    if (!await canManageCashbookPermissions(businessId)) {
      throw Exception(
          'You do not have permission to manage cashbook permissions');
    }

    return await _memberRepository.updateCashbookPermission(
      permissionId,
      newPermission,
    );
  }

  /// Validate email format
  bool _isValidEmail(String email) {
    return RegExp(r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$')
        .hasMatch(email);
  }

  /// Generate invitation link
  String generateInvitationLink(String token) {
    // This would be your app's deep link or web URL
    return 'https://your-app.com/invite/$token';
  }

  /// Get invitation summary for business
  Future<Map<String, int>> getInvitationSummary(String businessId) async {
    final invitations = await getBusinessInvitations(businessId);

    return {
      'total': invitations.length,
      'pending':
          invitations.where((i) => i.status == InvitationStatus.pending).length,
      'accepted': invitations
          .where((i) => i.status == InvitationStatus.accepted)
          .length,
      'declined': invitations
          .where((i) => i.status == InvitationStatus.declined)
          .length,
      'expired':
          invitations.where((i) => i.status == InvitationStatus.expired).length,
    };
  }

  /// Get member summary for business
  Future<Map<String, int>> getMemberSummary(String businessId) async {
    final members = await getBusinessMembers(businessId);

    return {
      'total': members.length,
      'partners': members.where((m) => m.role == MemberRole.partner).length,
      'admins': members.where((m) => m.role == MemberRole.admin).length,
      'staff': members.where((m) => m.role == MemberRole.staff).length,
    };
  }

  /// Get invitations received by the current user
  Future<List<BusinessInvitationModel>> getReceivedInvitations() async {
    try {
      return await _memberRepository.getReceivedInvitations();
    } catch (error) {
      throw Exception('Failed to get received invitations: $error');
    }
  }

  /// Respond to an invitation (accept or decline)
  Future<void> respondToInvitation(String invitationId, bool accept) async {
    try {
      await _memberRepository.respondToInvitation(invitationId, accept);
    } catch (error) {
      throw Exception('Failed to respond to invitation: $error');
    }
  }
}
