import 'package:supabase_flutter/supabase_flutter.dart';
import '../models/models.dart';
import '../repositories/user_repository.dart';
import 'supabase_service.dart';

class AuthService {
  static AuthService? _instance;
  static AuthService get instance => _instance ??= AuthService._();

  AuthService._();

  final SupabaseService _supabaseService = SupabaseService.instance;
  final UserRepository _userRepository = UserRepository();

  /// Check if user is authenticated
  bool get isAuthenticated => _supabaseService.isAuthenticated;

  /// Get current user
  User? get currentUser => _supabaseService.currentUser;

  /// Get current user ID
  String? get currentUserId => _supabaseService.currentUserId;

  /// Listen to auth state changes
  Stream<AuthState> get authStateChanges => _supabaseService.authStateChanges;

  /// Sign up with email and password
  Future<UserModel> signUp({
    required String email,
    required String password,
    String? fullName,
    String? phone,
  }) async {
    try {
      final authResponse = await _supabaseService.signUpWithEmailPassword(
        email: email,
        password: password,
        data: {
          'full_name': fullName,
          'phone': phone,
        },
      );

      if (authResponse.user == null) {
        throw Exception('Failed to create user account');
      }

      // Create user profile in our users table
      final userModel = await _userRepository.createProfile(
        id: authResponse.user!.id,
        email: email,
        fullName: fullName,
        phone: phone,
      );

      return userModel;
    } catch (error) {
      throw Exception(_handleAuthError(error));
    }
  }

  /// Sign in with email and password
  Future<UserModel> signIn({
    required String email,
    required String password,
  }) async {
    try {
      final authResponse = await _supabaseService.signInWithEmailPassword(
        email: email,
        password: password,
      );

      if (authResponse.user == null) {
        throw Exception('Failed to sign in');
      }

      // Get user profile
      final userModel = await _userRepository.getCurrentUser();
      if (userModel == null) {
        throw Exception('User profile not found');
      }

      return userModel;
    } catch (error) {
      throw Exception(_handleAuthError(error));
    }
  }

  /// Sign out
  Future<void> signOut() async {
    try {
      await _supabaseService.signOut();
    } catch (error) {
      throw Exception(_handleAuthError(error));
    }
  }

  /// Reset password
  Future<void> resetPassword(String email) async {
    try {
      await _supabaseService.resetPassword(email);
    } catch (error) {
      throw Exception(_handleAuthError(error));
    }
  }

  /// Update password
  Future<void> updatePassword(String newPassword) async {
    try {
      await _supabaseService.client.auth.updateUser(
        UserAttributes(password: newPassword),
      );
    } catch (error) {
      throw Exception(_handleAuthError(error));
    }
  }

  /// Update email
  Future<void> updateEmail(String newEmail) async {
    try {
      await _supabaseService.client.auth.updateUser(
        UserAttributes(email: newEmail),
      );
    } catch (error) {
      throw Exception(_handleAuthError(error));
    }
  }

  /// Get current user profile
  Future<UserModel?> getCurrentUserProfile() async {
    if (!isAuthenticated) return null;

    try {
      return await _userRepository.getCurrentUser();
    } catch (error) {
      throw Exception(_handleAuthError(error));
    }
  }

  /// Update user profile
  Future<UserModel> updateProfile({
    String? fullName,
    String? avatarUrl,
    String? phone,
  }) async {
    if (!isAuthenticated) {
      throw Exception('User must be authenticated');
    }

    try {
      return await _userRepository.updateProfile(
        fullName: fullName,
        avatarUrl: avatarUrl,
        phone: phone,
      );
    } catch (error) {
      throw Exception(_handleAuthError(error));
    }
  }

  /// Delete account
  Future<void> deleteAccount() async {
    if (!isAuthenticated) {
      throw Exception('User must be authenticated');
    }

    try {
      // Delete user profile and related data
      await _userRepository.deleteAccount();

      // Sign out
      await signOut();
    } catch (error) {
      throw Exception(_handleAuthError(error));
    }
  }

  /// Handle authentication errors
  String _handleAuthError(dynamic error) {
    if (error is AuthException) {
      switch (error.message.toLowerCase()) {
        case 'invalid login credentials':
          return 'Invalid email or password';
        case 'email not confirmed':
          return 'Please confirm your email address';
        case 'user not found':
          return 'No account found with this email';
        case 'email already registered':
          return 'An account with this email already exists';
        case 'password should be at least 6 characters':
          return 'Password must be at least 6 characters long';
        case 'signup is disabled':
          return 'Account registration is currently disabled';
        case 'email rate limit exceeded':
          return 'Too many email requests. Please try again later';
        default:
          return error.message;
      }
    } else if (error is Exception) {
      return error.toString().replaceFirst('Exception: ', '');
    }

    return 'An unexpected error occurred';
  }

  /// Check if email is available
  Future<bool> isEmailAvailable(String email) async {
    try {
      // Try to sign in with a dummy password to check if email exists
      // This is a workaround since Supabase doesn't provide a direct way to check email availability
      await _supabaseService.client.auth.signInWithPassword(
        email: email,
        password: 'dummy_password_that_will_fail',
      );
      return false; // If no exception, email exists
    } catch (error) {
      if (error is AuthException &&
          error.message.toLowerCase().contains('invalid login credentials')) {
        return false; // Email exists but password is wrong
      }
      return true; // Email doesn't exist or other error
    }
  }

  /// Refresh session
  Future<void> refreshSession() async {
    try {
      await _supabaseService.refreshSession();
    } catch (error) {
      throw Exception(_handleAuthError(error));
    }
  }
}
