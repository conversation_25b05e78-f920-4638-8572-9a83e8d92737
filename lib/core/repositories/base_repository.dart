import 'package:supabase_flutter/supabase_flutter.dart';
import '../services/supabase_service.dart';

abstract class BaseRepository {
  final SupabaseClient _client = SupabaseService.instance.client;
  
  SupabaseClient get client => _client;
  String? get currentUserId => SupabaseService.instance.currentUserId;

  /// Handle Supabase exceptions and convert to user-friendly messages
  String handleException(dynamic error) {
    if (error is PostgrestException) {
      switch (error.code) {
        case '23505':
          return 'This record already exists';
        case '23503':
          return 'Cannot delete this record as it is being used elsewhere';
        case '42501':
          return 'You do not have permission to perform this action';
        default:
          return error.message;
      }
    } else if (error is AuthException) {
      switch (error.message) {
        case 'Invalid login credentials':
          return 'Invalid email or password';
        case 'Email not confirmed':
          return 'Please confirm your email address';
        default:
          return error.message;
      }
    } else if (error is StorageException) {
      return 'File upload failed: ${error.message}';
    }
    
    return error.toString();
  }

  /// Execute a query with error handling
  Future<T> executeQuery<T>(Future<T> Function() query) async {
    try {
      return await query();
    } catch (error) {
      throw Exception(handleException(error));
    }
  }

  /// Check if user is authenticated
  void requireAuth() {
    if (currentUserId == null) {
      throw Exception('User must be authenticated to perform this action');
    }
  }
}