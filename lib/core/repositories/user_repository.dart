import '../models/models.dart';
import 'base_repository.dart';

class UserRepository extends BaseRepository {
  static const String _tableName = 'users';

  /// Get current user profile
  Future<UserModel?> getCurrentUser() async {
    requireAuth();

    return executeQuery(() async {
      final response = await client
          .from(_tableName)
          .select()
          .eq('id', currentUserId!)
          .maybeSingle();

      return response != null ? UserModel.fromJson(response) : null;
    });
  }

  /// Update user profile
  Future<UserModel> updateProfile({
    String? fullName,
    String? avatarUrl,
    String? phone,
  }) async {
    requireAuth();

    return executeQuery(() async {
      final updateData = <String, dynamic>{
        'updated_at': DateTime.now().toIso8601String(),
      };

      if (fullName != null) updateData['full_name'] = fullName;
      if (avatarUrl != null) updateData['avatar_url'] = avatarUrl;
      if (phone != null) updateData['phone'] = phone;

      final response = await client
          .from(_tableName)
          .update(updateData)
          .eq('id', currentUserId!)
          .select()
          .single();

      return UserModel.fromJson(response);
    });
  }

  /// Create user profile (called after signup)
  Future<UserModel> createProfile({
    required String id,
    required String email,
    String? fullName,
    String? avatarUrl,
    String? phone,
  }) async {
    await client.rpc('create_profile', params: {
      'uid': id,
      'email': email,
      'full_name': fullName,
      'avatar_url': avatarUrl,
      'phone': phone,
    });
    return UserModel(
      id: id,
      email: email,
      fullName: fullName,
      avatarUrl: avatarUrl,
      phone: phone,
      isActive: true,
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    );
  }

  /// Deactivate user account
  Future<void> deactivateAccount() async {
    requireAuth();

    return executeQuery(() async {
      await client.from(_tableName).update({
        'is_active': false,
        'updated_at': DateTime.now().toIso8601String(),
      }).eq('id', currentUserId!);
    });
  }

  /// Delete user account and all related data
  Future<void> deleteAccount() async {
    requireAuth();

    return executeQuery(() async {
      // Note: Due to CASCADE constraints, this will delete all related data
      await client.from(_tableName).delete().eq('id', currentUserId!);
    });
  }
}
