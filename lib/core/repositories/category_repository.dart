import '../models/models.dart';
import 'base_repository.dart';

class CategoryRepository extends BaseRepository {
  static const String _tableName = 'categories';

  /// Get all categories
  Future<List<CategoryModel>> getAllCategories() async {
    return executeQuery(() async {
      final response = await client
          .from(_tableName)
          .select()
          .eq('is_active', true)
          .order('sort_order', ascending: true)
          .order('name', ascending: true);

      return response.map((json) => CategoryModel.fromJson(json)).toList();
    });
  }

  /// Get categories by template
  Future<List<CategoryModel>> getCategoriesByTemplate(String templateId) async {
    return executeQuery(() async {
      final response = await client
          .from(_tableName)
          .select()
          .eq('template_id', templateId)
          .eq('is_active', true)
          .order('sort_order', ascending: true)
          .order('name', ascending: true);

      return response.map((json) => CategoryModel.fromJson(json)).toList();
    });
  }

  /// Get categories by type
  Future<List<CategoryModel>> getCategoriesByType(CategoryType type) async {
    return executeQuery(() async {
      final response = await client
          .from(_tableName)
          .select()
          .eq('type', type.name)
          .eq('is_active', true)
          .order('sort_order', ascending: true)
          .order('name', ascending: true);

      return response.map((json) => CategoryModel.fromJson(json)).toList();
    });
  }

  /// Get income categories
  Future<List<CategoryModel>> getIncomeCategories() async {
    return getCategoriesByType(CategoryType.income);
  }

  /// Get expense categories
  Future<List<CategoryModel>> getExpenseCategories() async {
    return getCategoriesByType(CategoryType.expense);
  }

  /// Get category by ID
  Future<CategoryModel?> getCategoryById(String categoryId) async {
    return executeQuery(() async {
      final response = await client
          .from(_tableName)
          .select()
          .eq('id', categoryId)
          .maybeSingle();

      return response != null ? CategoryModel.fromJson(response) : null;
    });
  }

  /// Get parent categories (categories without parent_id)
  Future<List<CategoryModel>> getParentCategories({CategoryType? type}) async {
    return executeQuery(() async {
      var query = client
          .from(_tableName)
          .select()
          .eq('is_parent', false)
          .eq('is_active', true);

      if (type != null) {
        query = query.eq('type', type.name);
      }

      final response = await query;
      return response.map((json) => CategoryModel.fromJson(json)).toList();
    });
  }

  /// Get subcategories for a parent category
  Future<List<CategoryModel>> getSubcategories(String parentId) async {
    return executeQuery(() async {
      final response = await client
          .from(_tableName)
          .select()
          .eq('parent_id', parentId)
          .eq('is_active', true)
          .order('sort_order', ascending: true)
          .order('name', ascending: true);

      return response.map((json) => CategoryModel.fromJson(json)).toList();
    });
  }

  /// Get categories with their subcategories
  Future<Map<CategoryModel, List<CategoryModel>>>
      getCategoriesWithSubcategories({
    CategoryType? type,
    String? templateId,
  }) async {
    return executeQuery(() async {
      // Get parent categories
      var parentQuery = client.from(_tableName).select();

      if (type != null) {
        parentQuery = parentQuery.eq('type', type.name);
      }

      if (templateId != null) {
        parentQuery = parentQuery.eq('template_id', templateId);
      }

      final parentResponse = await parentQuery;
      final parentCategories =
          parentResponse.map((json) => CategoryModel.fromJson(json)).toList();

      // Get all subcategories
      var subQuery = client
          .from(_tableName)
          .select()
          .not('parent_id', 'is', null)
          .eq('is_active', true);

      if (type != null) {
        subQuery = subQuery.eq('type', type.name);
      }

      if (templateId != null) {
        subQuery = subQuery.eq('template_id', templateId);
      }

      // subQuery = subQuery
      //     .order('sort_order', ascending: true)
      //     .order('name', ascending: true);

      final subResponse = await subQuery;
      final subcategories =
          subResponse.map((json) => CategoryModel.fromJson(json)).toList();

      // Group subcategories by parent
      final result = <CategoryModel, List<CategoryModel>>{};
      for (final parent in parentCategories) {
        result[parent] =
            subcategories.where((sub) => sub.parentId == parent.id).toList();
      }

      return result;
    });
  }

  /// Search categories
  Future<List<CategoryModel>> searchCategories({
    required String searchTerm,
    CategoryType? type,
    String? templateId,
  }) async {
    return executeQuery(() async {
      var query = client
          .from(_tableName)
          .select()
          .ilike('name', '%$searchTerm%')
          .eq('is_active', true);

      if (type != null) {
        query = query.eq('type', type.name);
      }

      if (templateId != null) {
        query = query.eq('template_id', templateId);
      }

      // query = query
      //     .order('sort_order', ascending: true)
      //     .order('name', ascending: true);

      final response = await query;
      return response.map((json) => CategoryModel.fromJson(json)).toList();
    });
  }

  /// Get most used categories (requires transaction data)
  Future<List<CategoryModel>> getMostUsedCategories({
    CategoryType? type,
    int limit = 10,
  }) async {
    requireAuth();

    return executeQuery(() async {
      var query = client.rpc('get_most_used_categories', params: {
        'p_user_id': currentUserId!,
        'p_type': type?.name,
        'p_limit': limit,
      });

      final response = await query;
      return (response as List)
          .map((json) => CategoryModel.fromJson(json))
          .toList();
    });
  }
}
