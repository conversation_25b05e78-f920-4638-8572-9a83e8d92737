import '../models/models.dart';
import 'base_repository.dart';

class CashbookTemplateRepository extends BaseRepository {
  static const String _tableName = 'cashbook_templates';

  /// Get all active cashbook templates
  Future<List<CashbookTemplateModel>> getAllTemplates() async {
    return executeQuery(() async {
      final response = await client
          .from(_tableName)
          .select()
          .eq('is_active', true)
          .order('name', ascending: true);

      return response.map((json) => CashbookTemplateModel.fromJson(json)).toList();
    });
  }

  /// Get template by ID
  Future<CashbookTemplateModel?> getTemplateById(String templateId) async {
    return executeQuery(() async {
      final response = await client
          .from(_tableName)
          .select()
          .eq('id', templateId)
          .maybeSingle();

      return response != null ? CashbookTemplateModel.fromJson(response) : null;
    });
  }

  /// Search templates
  Future<List<CashbookTemplateModel>> searchTemplates(String searchTerm) async {
    return executeQuery(() async {
      final response = await client
          .from(_tableName)
          .select()
          .or('name.ilike.%$searchTerm%,description.ilike.%$searchTerm%')
          .eq('is_active', true)
          .order('name', ascending: true);

      return response.map((json) => CashbookTemplateModel.fromJson(json)).toList();
    });
  }

  /// Get template with its categories
  Future<Map<String, dynamic>> getTemplateWithCategories(String templateId) async {
    return executeQuery(() async {
      // Get template
      final templateResponse = await client
          .from(_tableName)
          .select()
          .eq('id', templateId)
          .single();

      final template = CashbookTemplateModel.fromJson(templateResponse);

      // Get categories for this template
      final categoriesResponse = await client
          .from('categories')
          .select()
          .eq('template_id', templateId)
          .eq('is_active', true)
          .order('sort_order', ascending: true)
          .order('name', ascending: true);

      final categories = categoriesResponse.map((json) => CategoryModel.fromJson(json)).toList();

      return {
        'template': template,
        'categories': categories,
      };
    });
  }

  /// Get popular templates (most used)
  Future<List<CashbookTemplateModel>> getPopularTemplates({int limit = 5}) async {
    return executeQuery(() async {
      var query = client
          .rpc('get_popular_templates', params: {
            'p_limit': limit,
          });

      final response = await query;
      return (response as List).map((json) => CashbookTemplateModel.fromJson(json)).toList();
    });
  }

  /// Get recommended templates for user (based on usage patterns)
  Future<List<CashbookTemplateModel>> getRecommendedTemplates({int limit = 3}) async {
    requireAuth();

    return executeQuery(() async {
      var query = client
          .rpc('get_recommended_templates', params: {
            'p_user_id': currentUserId!,
            'p_limit': limit,
          });

      final response = await query;
      return (response as List).map((json) => CashbookTemplateModel.fromJson(json)).toList();
    });
  }
}