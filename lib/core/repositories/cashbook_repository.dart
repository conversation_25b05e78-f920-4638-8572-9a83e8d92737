import '../models/models.dart';
import 'base_repository.dart';

class CashbookRepository extends BaseRepository {
  static const String _tableName = 'cashbooks';
  static const String _memberCashbookPermissions =
      'member_cashbook_permissions';
  static const String _businessMembers = 'business_members';

  /// Get all cashbooks for current user
  Future<List<CashbookModel>> getUserCashbooks(String businessId) async {
    requireAuth();

    return executeQuery(() async {
      final response = await client
          .from(_tableName)
          .select()
          .or('business_id.eq.$businessId,user_id.eq.$currentUserId')
          .eq('is_active', true)
          .order('last_activity', ascending: false);

      return response.map((json) => CashbookModel.fromJson(json)).toList();
    });
  }

  Future<int> countCashbookMembers(CashbookModel cashbook) async {
    requireAuth();
    return executeQuery(() async {
      final response = await client
          .from(_businessMembers)
          .select()
          .eq('business_id', cashbook.businessId!)
          .count();

      return response.count;
    });
  }

  // get user cashbook permissions
  Future<List> getUserCashbookPermissions(String cashbookId) async {
    requireAuth();

    return executeQuery(() async {
      final response = await client
          .from(_memberCashbookPermissions)
          .select('permission')
          .eq('cashbook_id', cashbookId)
          .eq('user_id', currentUserId!);

      return response.map((e) => e['permission']).toList();
    });
  }

  /// Get cashbook by ID
  Future<CashbookModel?> getCashbookById(String cashbookId) async {
    requireAuth();

    return executeQuery(() async {
      final response = await client
          .from(_tableName)
          .select()
          .eq('id', cashbookId)
          .maybeSingle();

      return response != null ? CashbookModel.fromJson(response) : null;
    });
  }

  /// Get default cashbook
  Future<CashbookModel?> getDefaultCashbook() async {
    requireAuth();

    return executeQuery(() async {
      final response = await client
          .from(_tableName)
          .select()
          .eq('user_id', currentUserId!)
          .eq('is_default', true)
          .eq('is_active', true)
          .maybeSingle();

      return response != null ? CashbookModel.fromJson(response) : null;
    });
  }

  /// Get cashbooks by business
  Future<List<CashbookModel>> getCashbooksByBusiness(String businessId) async {
    requireAuth();

    return executeQuery(() async {
      final response = await client
          .from(_tableName)
          .select()
          .eq('business_id', businessId)
          .eq('is_active', true)
          .order('last_activity', ascending: false);

      return response.map((json) => CashbookModel.fromJson(json)).toList();
    });
  }

  /// Create new cashbook
  Future<CashbookModel> createCashbook({
    required String name,
    String? businessId,
    String? templateId,
    String? description,
    String currency = 'USD',
    String? color,
    String? icon,
    bool isDefault = false,
  }) async {
    requireAuth();

    return executeQuery(() async {
      // If this is set as default, unset other defaults first
      if (isDefault) {
        await _unsetDefaultCashbooks();
      }

      final now = DateTime.now();
      final cashbookData = {
        'user_id': currentUserId!,
        'business_id': businessId,
        'template_id': templateId,
        'name': name,
        'description': description,
        'currency': currency,
        'color': color,
        'icon': icon,
        'balance': 0.0,
        'transaction_count': 0,
        'is_default': isDefault,
        'is_active': true,
        'last_activity': now.toIso8601String(),
        'created_at': now.toIso8601String(),
        'updated_at': now.toIso8601String(),
      };

      final response =
          await client.from(_tableName).insert(cashbookData).select().single();

      return CashbookModel.fromJson(response);
    });
  }

  // add user to cashbook member admin
  // set permission to write
  Future<void> addCashbookMeta(CashbookModel cashbook) async {
    requireAuth();

    return executeQuery(() async {
      final now = DateTime.now();
      final memberData = {
        'business_id': cashbook.businessId,
        'user_id': currentUserId,
        'role': 'admin',
        'invited_by': currentUserId,
        'joined_at': now,
        'is_active': true,
        'created_at': now,
        'updated_at': now,
      };

      final response = await client
          .from(_businessMembers)
          .insert(memberData)
          .select()
          .single();

      final permissionData = {
        'business_member_id': response['id'],
        'cashbook_id': cashbook.id,
        'permission': 'write',
        'granted_by': currentUserId,
        'created_at': now,
        'updated_at': now,
      };
      await client
          .from(_memberCashbookPermissions)
          .insert(permissionData)
          .select()
          .single();
    });
  }

  /// Update cashbook
  Future<CashbookModel> updateCashbook({
    required String cashbookId,
    String? name,
    String? businessId,
    String? templateId,
    String? description,
    String? currency,
    String? color,
    String? icon,
    bool? isDefault,
    bool? isActive,
  }) async {
    requireAuth();

    return executeQuery(() async {
      // If this is set as default, unset other defaults first
      if (isDefault == true) {
        await _unsetDefaultCashbooks();
      }

      final updateData = <String, dynamic>{
        'updated_at': DateTime.now().toIso8601String(),
      };

      if (name != null) updateData['name'] = name;
      if (businessId != null) updateData['business_id'] = businessId;
      if (templateId != null) updateData['template_id'] = templateId;
      if (description != null) updateData['description'] = description;
      if (currency != null) updateData['currency'] = currency;
      if (color != null) updateData['color'] = color;
      if (icon != null) updateData['icon'] = icon;
      if (isDefault != null) updateData['is_default'] = isDefault;
      if (isActive != null) updateData['is_active'] = isActive;

      final response = await client
          .from(_tableName)
          .update(updateData)
          .eq('id', cashbookId)
          .eq('user_id', currentUserId!)
          .select()
          .single();

      return CashbookModel.fromJson(response);
    });
  }

  /// Delete cashbook (soft delete by setting is_active to false)
  Future<void> deleteCashbook(String cashbookId) async {
    requireAuth();

    return executeQuery(() async {
      await client
          .from(_tableName)
          .update({
            'is_active': false,
            'updated_at': DateTime.now().toIso8601String(),
          })
          .eq('id', cashbookId)
          .eq('user_id', currentUserId!);
    });
  }

  /// Permanently delete cashbook
  Future<void> permanentlyDeleteCashbook(String cashbookId) async {
    requireAuth();

    return executeQuery(() async {
      await client
          .from(_tableName)
          .delete()
          .eq('id', cashbookId)
          .eq('user_id', currentUserId!);
    });
  }

  /// Set cashbook as default
  Future<void> setDefaultCashbook(String cashbookId) async {
    requireAuth();

    return executeQuery(() async {
      // First unset all defaults
      await _unsetDefaultCashbooks();

      // Then set the specified cashbook as default
      await client
          .from(_tableName)
          .update({
            'is_default': true,
            'updated_at': DateTime.now().toIso8601String(),
          })
          .eq('id', cashbookId)
          .eq('user_id', currentUserId!);
    });
  }

  /// Get cashbook statistics
  Future<Map<String, dynamic>> getCashbookStats(String cashbookId) async {
    requireAuth();

    return executeQuery(() async {
      final response = await client
          .from(_tableName)
          .select('balance, transaction_count, last_activity')
          .eq('id', cashbookId)
          .eq('user_id', currentUserId!)
          .single();

      return response;
    });
  }

  /// Helper method to unset all default cashbooks
  Future<void> _unsetDefaultCashbooks() async {
    await client
        .from(_tableName)
        .update({
          'is_default': false,
          'updated_at': DateTime.now().toIso8601String(),
        })
        .eq('user_id', currentUserId!)
        .eq('is_default', true);
  }
}
