import '../models/models.dart';
import 'base_repository.dart';

class PaymentModeRepository extends BaseRepository {
  static const String _tableName = 'payment_modes';

  /// Get all payment modes
  Future<List<PaymentModeModel>> getAllPaymentModes() async {
    return executeQuery(() async {
      final response = await client
          .from(_tableName)
          .select()
          .eq('is_active', true)
          .order('sort_order', ascending: true)
          .order('name', ascending: true);

      return response.map((json) => PaymentModeModel.fromJson(json)).toList();
    });
  }

  /// Get payment mode by ID
  Future<PaymentModeModel?> getPaymentModeById(String paymentModeId) async {
    return executeQuery(() async {
      final response = await client
          .from(_tableName)
          .select()
          .eq('id', paymentModeId)
          .maybeSingle();

      return response != null ? PaymentModeModel.fromJson(response) : null;
    });
  }

  /// Search payment modes
  Future<List<PaymentModeModel>> searchPaymentModes(String searchTerm) async {
    return executeQuery(() async {
      final response = await client
          .from(_tableName)
          .select()
          .ilike('name', '%$searchTerm%')
          .eq('is_active', true)
          .order('sort_order', ascending: true)
          .order('name', ascending: true);

      return response.map((json) => PaymentModeModel.fromJson(json)).toList();
    });
  }

  /// Get most used payment modes (requires transaction data)
  Future<List<PaymentModeModel>> getMostUsedPaymentModes({int limit = 10}) async {
    requireAuth();

    return executeQuery(() async {
      var query = client
          .rpc('get_most_used_payment_modes', params: {
            'p_user_id': currentUserId!,
            'p_limit': limit,
          });

      final response = await query;
      return (response as List).map((json) => PaymentModeModel.fromJson(json)).toList();
    });
  }

  /// Get payment modes by usage frequency
  Future<List<Map<String, dynamic>>> getPaymentModeUsageStats() async {
    requireAuth();

    return executeQuery(() async {
      var query = client
          .rpc('get_payment_mode_usage_stats', params: {
            'p_user_id': currentUserId!,
          });

      final response = await query;
      return (response as List).cast<Map<String, dynamic>>();
    });
  }
}