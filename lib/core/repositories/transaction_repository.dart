import '../models/models.dart';
import 'base_repository.dart';

class TransactionRepository extends BaseRepository {
  static const String _tableName = 'transactions';

  /// Get transactions for a cashbook
  Future<List<TransactionModel>> getCashbookTransactions({
    required String cashbookId,
    int? limit,
    int? offset,
    DateTime? startDate,
    DateTime? endDate,
    TransactionType? type,
    String? categoryId,
  }) async {
    requireAuth();

    return executeQuery(() async {
      var query =
          client.from(_tableName).select().eq('cashbook_id', cashbookId);

      if (startDate != null) {
        query = query.gte(
            'transaction_date', startDate.toIso8601String().split('T')[0]);
      }

      if (endDate != null) {
        query = query.lte(
            'transaction_date', endDate.toIso8601String().split('T')[0]);
      }

      if (type != null) {
        query = query.eq('type', type.name);
      }

      if (categoryId != null) {
        query = query.eq('category_id', categoryId);
      }

      // query = query.order('transaction_date', ascending: false)
      //             .order('created_at', ascending: false);

      // if (limit != null) {
      //   query = query.limit(limit);
      // }

      if (offset != null) {
        // query = query.range(offset, offset + (limit ?? 50) - 1);
      }

      final response = await query;
      return response.map((json) => TransactionModel.fromJson(json)).toList();
    });
  }

  /// Get all transactions for the current user across all cashbooks
  Future<List<TransactionModel>> getUserTransactions({
    int? limit,
    int? offset,
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    requireAuth();

    return executeQuery(() async {
      var query = client
          .from(_tableName)
          .select('''
            *,
            categories:category_id (
              id,
              name,
              icon,
              color
            ),
            cashbooks:cashbook_id (
              id,
              name,
              business_id
            )
          ''')
          .eq('user_id', currentUserId!)
          .order('transaction_date', ascending: false);

      // Apply pagination
      if (limit != null) {
        query = query.limit(limit);
      }
      if (offset != null) {
        query = query.range(offset, offset + (limit ?? 50) - 1);
      }

      final response = await query;
      var transactions =
          response.map((json) => TransactionModel.fromJson(json)).toList();

      // Apply date filters in memory
      if (startDate != null || endDate != null) {
        transactions = transactions.where((transaction) {
          if (startDate != null &&
              transaction.transactionDate.isBefore(startDate)) {
            return false;
          }
          if (endDate != null && transaction.transactionDate.isAfter(endDate)) {
            return false;
          }
          return true;
        }).toList();
      }

      return transactions;
    });
  }

  /// Get transaction by ID
  Future<TransactionModel?> getTransactionById(String transactionId) async {
    requireAuth();

    return executeQuery(() async {
      final response = await client
          .from(_tableName)
          .select()
          .eq('id', transactionId)
          .maybeSingle();

      return response != null ? TransactionModel.fromJson(response) : null;
    });
  }

  /// Create new transaction
  Future<TransactionModel> createTransaction({
    required String cashbookId,
    required TransactionType type,
    required double amount,
    required String description,
    required DateTime transactionDate,
    String? categoryId,
    String? paymentModeId,
    String? notes,
    String? referenceNumber,
  }) async {
    requireAuth();

    return executeQuery(() async {
      final now = DateTime.now();
      final transactionData = {
        'cashbook_id': cashbookId,
        'user_id': currentUserId!,
        'category_id': categoryId,
        'payment_mode_id': paymentModeId,
        'type': type.name,
        'amount': amount,
        'description': description,
        'notes': notes,
        'reference_number': referenceNumber,
        'transaction_date': transactionDate.toIso8601String().split('T')[0],
        'created_at': now.toIso8601String(),
        'updated_at': now.toIso8601String(),
      };

      final response = await client
          .from(_tableName)
          .insert(transactionData)
          .select()
          .single();

      return TransactionModel.fromJson(response);
    });
  }

  /// Update transaction
  Future<TransactionModel> updateTransaction({
    required String transactionId,
    TransactionType? type,
    double? amount,
    String? description,
    DateTime? transactionDate,
    String? categoryId,
    String? paymentModeId,
    String? notes,
    String? referenceNumber,
  }) async {
    requireAuth();

    return executeQuery(() async {
      final updateData = <String, dynamic>{
        'updated_at': DateTime.now().toIso8601String(),
      };

      if (type != null) updateData['type'] = type.name;
      if (amount != null) updateData['amount'] = amount;
      if (description != null) updateData['description'] = description;
      if (transactionDate != null) {
        updateData['transaction_date'] =
            transactionDate.toIso8601String().split('T')[0];
      }
      if (categoryId != null) updateData['category_id'] = categoryId;
      if (paymentModeId != null) updateData['payment_mode_id'] = paymentModeId;
      if (notes != null) updateData['notes'] = notes;
      if (referenceNumber != null)
        updateData['reference_number'] = referenceNumber;

      final response = await client
          .from(_tableName)
          .update(updateData)
          .eq('id', transactionId)
          .eq('user_id', currentUserId!)
          .select()
          .single();

      return TransactionModel.fromJson(response);
    });
  }

  /// Delete transaction
  Future<void> deleteTransaction(String transactionId) async {
    requireAuth();

    return executeQuery(() async {
      await client
          .from(_tableName)
          .delete()
          .eq('id', transactionId)
          .eq('user_id', currentUserId!);
    });
  }

  /// Get transaction summary for a cashbook
  Future<Map<String, dynamic>> getTransactionSummary({
    required String cashbookId,
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    requireAuth();

    return executeQuery(() async {
      var query = client.rpc('get_transaction_summary', params: {
        'p_cashbook_id': cashbookId,
        'p_user_id': currentUserId!,
        'p_start_date': startDate?.toIso8601String().split('T')[0],
        'p_end_date': endDate?.toIso8601String().split('T')[0],
      });

      final response = await query;
      return response as Map<String, dynamic>;
    });
  }

  /// Get transactions by category
  Future<List<TransactionModel>> getTransactionsByCategory({
    required String cashbookId,
    required String categoryId,
    DateTime? startDate,
    DateTime? endDate,
    int? limit,
  }) async {
    requireAuth();

    return executeQuery(() async {
      var query = client
          .from(_tableName)
          .select()
          .eq('cashbook_id', cashbookId)
          .eq('user_id', currentUserId!)
          .eq('category_id', categoryId);

      if (startDate != null) {
        query = query.gte(
            'transaction_date', startDate.toIso8601String().split('T')[0]);
      }

      if (endDate != null) {
        query = query.lte(
            'transaction_date', endDate.toIso8601String().split('T')[0]);
      }

      // query = query.order('transaction_date', ascending: false);

      // if (limit != null) {
      //   query = query.limit(limit);
      // }

      final response = await query;
      return response.map((json) => TransactionModel.fromJson(json)).toList();
    });
  }

  /// Search transactions
  Future<List<TransactionModel>> searchTransactions({
    required String cashbookId,
    required String searchTerm,
    int? limit,
  }) async {
    requireAuth();

    return executeQuery(() async {
      var query = client
          .from(_tableName)
          .select()
          .eq('cashbook_id', cashbookId)
          .eq('user_id', currentUserId!)
          .or('description.ilike.%$searchTerm%,notes.ilike.%$searchTerm%,reference_number.ilike.%$searchTerm%');

      // query = query.order('transaction_date', ascending: false);

      // if (limit != null) {
      //   query = query.limit(limit);
      // }

      final response = await query;
      return response.map((json) => TransactionModel.fromJson(json)).toList();
    });
  }

  /// Get recent transactions across all cashbooks
  Future<List<TransactionModel>> getRecentTransactions(
      String cashbookId, int? limit) async {
    requireAuth();

    return executeQuery(() async {
      final response = await client
          .from(_tableName)
          .select()
          .eq('cashbook_id', cashbookId)
          .order('created_at', ascending: false)
          .limit(limit ??= 10);
      return response.map((json) => TransactionModel.fromJson(json)).toList();
    });
  }
}
