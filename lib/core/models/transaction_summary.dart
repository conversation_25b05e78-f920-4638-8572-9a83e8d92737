class TransactionSummary {
  final double totalIncome;
  final double totalExpense;
  final double netAmount;
  final int totalTransactions;
  final int incomeTransactions;
  final int expenseTransactions;
  final DateTime? startDate;
  final DateTime? endDate;

  TransactionSummary({
    required this.totalIncome,
    required this.totalExpense,
    required this.netAmount,
    required this.totalTransactions,
    required this.incomeTransactions,
    required this.expenseTransactions,
    this.startDate,
    this.endDate,
  });

  factory TransactionSummary.fromJson(Map<String, dynamic> json) {
    return TransactionSummary(
      totalIncome: (json['total_income'] as num?)?.toDouble() ?? 0.0,
      totalExpense: (json['total_expense'] as num?)?.toDouble() ?? 0.0,
      netAmount: (json['net_amount'] as num?)?.toDouble() ?? 0.0,
      totalTransactions: json['total_transactions'] as int? ?? 0,
      incomeTransactions: json['income_transactions'] as int? ?? 0,
      expenseTransactions: json['expense_transactions'] as int? ?? 0,
      startDate: json['start_date'] != null ? DateTime.parse(json['start_date']) : null,
      endDate: json['end_date'] != null ? DateTime.parse(json['end_date']) : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'total_income': totalIncome,
      'total_expense': totalExpense,
      'net_amount': netAmount,
      'total_transactions': totalTransactions,
      'income_transactions': incomeTransactions,
      'expense_transactions': expenseTransactions,
      'start_date': startDate?.toIso8601String(),
      'end_date': endDate?.toIso8601String(),
    };
  }

  bool get hasProfit => netAmount > 0;
  bool get hasLoss => netAmount < 0;
  bool get isBreakEven => netAmount == 0;
}