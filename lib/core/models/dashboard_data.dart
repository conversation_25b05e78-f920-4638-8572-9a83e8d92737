import 'models.dart';

class DashboardData {
  final List<CashbookModel> cashbooks;
  final List<TransactionModel> recentTransactions;
  final CashbookModel? defaultCashbook;
  final double totalBalance;
  final int totalTransactions;
  final int activeCashbooks;
  final TransactionSummary? summary;

  DashboardData({
    required this.cashbooks,
    required this.recentTransactions,
    this.defaultCashbook,
    required this.totalBalance,
    required this.totalTransactions,
    required this.activeCashbooks,
    this.summary,
  });

  factory DashboardData.fromJson(Map<String, dynamic> json) {
    return DashboardData(
      cashbooks: (json['cashbooks'] as List?)
          ?.map((e) => CashbookModel.fromJson(e))
          .toList() ?? [],
      recentTransactions: (json['recent_transactions'] as List?)
          ?.map((e) => TransactionModel.fromJson(e))
          .toList() ?? [],
      defaultCashbook: json['default_cashbook'] != null 
          ? CashbookModel.fromJson(json['default_cashbook'])
          : null,
      totalBalance: (json['total_balance'] as num?)?.toDouble() ?? 0.0,
      totalTransactions: json['total_transactions'] as int? ?? 0,
      activeCashbooks: json['active_cashbooks'] as int? ?? 0,
      summary: json['summary'] != null 
          ? TransactionSummary.fromJson(json['summary'])
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'cashbooks': cashbooks.map((e) => e.toJson()).toList(),
      'recent_transactions': recentTransactions.map((e) => e.toJson()).toList(),
      'default_cashbook': defaultCashbook?.toJson(),
      'total_balance': totalBalance,
      'total_transactions': totalTransactions,
      'active_cashbooks': activeCashbooks,
      'summary': summary?.toJson(),
    };
  }

  bool get hasData => cashbooks.isNotEmpty;
  bool get hasTransactions => recentTransactions.isNotEmpty;
  bool get hasDefaultCashbook => defaultCashbook != null;
}