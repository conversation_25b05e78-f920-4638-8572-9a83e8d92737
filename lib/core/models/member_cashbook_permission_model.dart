enum CashbookPermission {
  read,
  write;

  String get displayName {
    switch (this) {
      case CashbookPermission.read:
        return 'Read Only';
      case CashbookPermission.write:
        return 'Read & Write';
    }
  }

  String get description {
    switch (this) {
      case CashbookPermission.read:
        return 'Can view transactions and reports';
      case CashbookPermission.write:
        return 'Can add, edit, and delete transactions';
    }
  }

  static CashbookPermission fromString(String permission) {
    switch (permission.toLowerCase()) {
      case 'read':
        return CashbookPermission.read;
      case 'write':
        return CashbookPermission.write;
      default:
        return CashbookPermission.read;
    }
  }
}

class MemberCashbookPermissionModel {
  final String id;
  final String businessMemberId;
  final String cashbookId;
  final CashbookPermission permission;
  final String? grantedBy;
  final DateTime createdAt;
  final DateTime updatedAt;

  // Additional info (populated via joins)
  final String? cashbookName;
  final String? memberName;
  final String? memberEmail;

  MemberCashbookPermissionModel({
    required this.id,
    required this.businessMemberId,
    required this.cashbookId,
    required this.permission,
    this.grantedBy,
    required this.createdAt,
    required this.updatedAt,
    this.cashbookName,
    this.memberName,
    this.memberEmail,
  });

  factory MemberCashbookPermissionModel.fromJson(Map<String, dynamic> json) {
    return MemberCashbookPermissionModel(
      id: json['id'] as String,
      businessMemberId: json['business_member_id'] as String,
      cashbookId: json['cashbook_id'] as String,
      permission: CashbookPermission.fromString(json['permission'] as String),
      grantedBy: json['granted_by'] as String?,
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: DateTime.parse(json['updated_at'] as String),
      cashbookName: json['cashbook_name'] as String?,
      memberName: json['member_name'] as String?,
      memberEmail: json['member_email'] as String?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'business_member_id': businessMemberId,
      'cashbook_id': cashbookId,
      'permission': permission.name,
      'granted_by': grantedBy,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
      'cashbook_name': cashbookName,
      'member_name': memberName,
      'member_email': memberEmail,
    };
  }

  MemberCashbookPermissionModel copyWith({
    String? id,
    String? businessMemberId,
    String? cashbookId,
    CashbookPermission? permission,
    String? grantedBy,
    DateTime? createdAt,
    DateTime? updatedAt,
    String? cashbookName,
    String? memberName,
    String? memberEmail,
  }) {
    return MemberCashbookPermissionModel(
      id: id ?? this.id,
      businessMemberId: businessMemberId ?? this.businessMemberId,
      cashbookId: cashbookId ?? this.cashbookId,
      permission: permission ?? this.permission,
      grantedBy: grantedBy ?? this.grantedBy,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      cashbookName: cashbookName ?? this.cashbookName,
      memberName: memberName ?? this.memberName,
      memberEmail: memberEmail ?? this.memberEmail,
    );
  }
}
