class CashbookTemplateModel {
  final String id;
  final String name;
  final String? description;
  final String? icon;
  final bool isActive;
  final DateTime createdAt;

  CashbookTemplateModel({
    required this.id,
    required this.name,
    this.description,
    this.icon,
    this.isActive = true,
    required this.createdAt,
  });

  factory CashbookTemplateModel.fromJson(Map<String, dynamic> json) {
    return CashbookTemplateModel(
      id: json['id'] as String,
      name: json['name'] as String,
      description: json['description'] as String?,
      icon: json['icon'] as String?,
      isActive: json['is_active'] as bool? ?? true,
      createdAt: DateTime.parse(json['created_at'] as String),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'icon': icon,
      'is_active': isActive,
      'created_at': createdAt.toIso8601String(),
    };
  }

  CashbookTemplateModel copyWith({
    String? id,
    String? name,
    String? description,
    String? icon,
    bool? isActive,
    DateTime? createdAt,
  }) {
    return CashbookTemplateModel(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      icon: icon ?? this.icon,
      isActive: isActive ?? this.isActive,
      createdAt: createdAt ?? this.createdAt,
    );
  }
}