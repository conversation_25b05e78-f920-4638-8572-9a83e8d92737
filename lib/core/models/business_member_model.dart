enum MemberRole {
  partner,
  admin,
  staff;

  String get displayName {
    switch (this) {
      case MemberRole.partner:
        return 'Partner';
      case MemberRole.admin:
        return 'Admin';
      case MemberRole.staff:
        return 'Staff';
    }
  }

  String get description {
    switch (this) {
      case MemberRole.partner:
        return 'Full access to business and financial data';
      case MemberRole.admin:
        return 'Manage team members and most business operations';
      case MemberRole.staff:
        return 'Limited access to assigned cashbooks';
    }
  }

  static MemberRole fromString(String role) {
    switch (role.toLowerCase()) {
      case 'partner':
        return MemberRole.partner;
      case 'admin':
        return MemberRole.admin;
      case 'staff':
        return MemberRole.staff;
      default:
        return MemberRole.staff;
    }
  }
}

class BusinessMemberModel {
  final String id;
  final String businessId;
  final String userId;
  final MemberRole role;
  final String? invitedBy;
  final DateTime joinedAt;
  final bool isActive;
  final DateTime createdAt;
  final DateTime updatedAt;

  // Additional user info (populated via join)
  final String? userEmail;
  final String? userFullName;
  final String? userAvatarUrl;

  BusinessMemberModel({
    required this.id,
    required this.businessId,
    required this.userId,
    required this.role,
    this.invitedBy,
    required this.joinedAt,
    this.isActive = true,
    required this.createdAt,
    required this.updatedAt,
    this.userEmail,
    this.userFullName,
    this.userAvatarUrl,
  });

  factory BusinessMemberModel.fromJson(Map<String, dynamic> json) {
    return BusinessMemberModel(
      id: json['id'] as String,
      businessId: json['business_id'] as String,
      userId: json['user_id'] as String,
      role: MemberRole.fromString(json['role'] as String),
      invitedBy: json['invited_by'] as String?,
      joinedAt: DateTime.parse(json['joined_at'] as String),
      isActive: json['is_active'] as bool? ?? true,
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: DateTime.parse(json['updated_at'] as String),
      userEmail: json['user_email'] as String?,
      userFullName: json['user_full_name'] as String?,
      userAvatarUrl: json['user_avatar_url'] as String?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'business_id': businessId,
      'user_id': userId,
      'role': role.name,
      'invited_by': invitedBy,
      'joined_at': joinedAt.toIso8601String(),
      'is_active': isActive,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
      'user_email': userEmail,
      'user_full_name': userFullName,
      'user_avatar_url': userAvatarUrl,
    };
  }

  BusinessMemberModel copyWith({
    String? id,
    String? businessId,
    String? userId,
    MemberRole? role,
    String? invitedBy,
    DateTime? joinedAt,
    bool? isActive,
    DateTime? createdAt,
    DateTime? updatedAt,
    String? userEmail,
    String? userFullName,
    String? userAvatarUrl,
  }) {
    return BusinessMemberModel(
      id: id ?? this.id,
      businessId: businessId ?? this.businessId,
      userId: userId ?? this.userId,
      role: role ?? this.role,
      invitedBy: invitedBy ?? this.invitedBy,
      joinedAt: joinedAt ?? this.joinedAt,
      isActive: isActive ?? this.isActive,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      userEmail: userEmail ?? this.userEmail,
      userFullName: userFullName ?? this.userFullName,
      userAvatarUrl: userAvatarUrl ?? this.userAvatarUrl,
    );
  }
}
