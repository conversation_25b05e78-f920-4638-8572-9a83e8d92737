class BusinessModel {
  final String id;
  final String userId;
  final String name;
  final String? description;
  final String? logoUrl;
  final String? address;
  final String? phone;
  final String? email;
  final String? website;
  final String? taxId;
  final String currency;
  final bool isDefault;
  final DateTime createdAt;
  final DateTime updatedAt;
  final String? role;

  BusinessModel({
    required this.id,
    required this.userId,
    required this.name,
    this.description,
    this.logoUrl,
    this.address,
    this.phone,
    this.email,
    this.website,
    this.taxId,
    this.role,
    this.currency = 'MWK',
    this.isDefault = false,
    required this.createdAt,
    required this.updatedAt,
  });

  factory BusinessModel.fromJson(Map<String, dynamic> json) {
    return BusinessModel(
      id: json['id'] as String,
      userId: json['user_id'] as String,
      name: json['name'] as String,
      description: json['description'] as String?,
      logoUrl: json['logo_url'] as String?,
      address: json['address'] as String?,
      phone: json['phone'] as String?,
      email: json['email'] as String?,
      website: json['website'] as String?,
      taxId: json['tax_id'] as String?,
      currency: json['currency'] as String? ?? 'USD',
      isDefault: json['is_default'] as bool? ?? false,
      role:
          (json['business_members'] != null && json['business_members'] is List)
              ? json['business_members'][0]['role'] as String?
              : null,
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: DateTime.parse(json['updated_at'] as String),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'user_id': userId,
      'name': name,
      'description': description,
      'logo_url': logoUrl,
      'address': address,
      'phone': phone,
      'email': email,
      'website': website,
      'tax_id': taxId,
      'currency': currency,
      'is_default': isDefault,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
      'role': role,
    };
  }

  BusinessModel copyWith({
    String? id,
    String? userId,
    String? name,
    String? description,
    String? logoUrl,
    String? address,
    String? phone,
    String? email,
    String? website,
    String? taxId,
    String? currency,
    bool? isDefault,
    DateTime? createdAt,
    DateTime? updatedAt,
    String? role,
  }) {
    return BusinessModel(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      name: name ?? this.name,
      description: description ?? this.description,
      logoUrl: logoUrl ?? this.logoUrl,
      address: address ?? this.address,
      phone: phone ?? this.phone,
      email: email ?? this.email,
      website: website ?? this.website,
      taxId: taxId ?? this.taxId,
      currency: currency ?? this.currency,
      isDefault: isDefault ?? this.isDefault,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      role: role ?? this.role,
    );
  }
}
