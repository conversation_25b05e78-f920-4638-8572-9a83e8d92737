class CashbookModel {
  final String id;
  final String userId;
  final String? businessId;
  final String? templateId;
  final String name;
  final String? description;
  final String currency;
  final String? color;
  final String? icon;
  final double balance;
  final int transactionCount;
  final bool isDefault;
  final bool isActive;
  final DateTime lastActivity;
  final DateTime createdAt;
  final DateTime updatedAt;

  CashbookModel({
    required this.id,
    required this.userId,
    this.businessId,
    this.templateId,
    required this.name,
    this.description,
    this.currency = 'USD',
    this.color,
    this.icon,
    this.balance = 0.0,
    this.transactionCount = 0,
    this.isDefault = false,
    this.isActive = true,
    required this.lastActivity,
    required this.createdAt,
    required this.updatedAt,
  });

  factory CashbookModel.fromJson(Map<String, dynamic> json) {
    return CashbookModel(
      id: json['id'] as String,
      userId: json['user_id'] as String,
      businessId: json['business_id'] as String?,
      templateId: json['template_id'] as String?,
      name: json['name'] as String,
      description: json['description'] as String?,
      currency: json['currency'] as String? ?? 'USD',
      color: json['color'] as String?,
      icon: json['icon'] as String?,
      balance: (json['balance'] as num?)?.toDouble() ?? 0.0,
      transactionCount: json['transaction_count'] as int? ?? 0,
      isDefault: json['is_default'] as bool? ?? false,
      isActive: json['is_active'] as bool? ?? true,
      lastActivity: DateTime.parse(json['last_activity'] as String),
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: DateTime.parse(json['updated_at'] as String),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'user_id': userId,
      'business_id': businessId,
      'template_id': templateId,
      'name': name,
      'description': description,
      'currency': currency,
      'color': color,
      'icon': icon,
      'balance': balance,
      'transaction_count': transactionCount,
      'is_default': isDefault,
      'is_active': isActive,
      'last_activity': lastActivity.toIso8601String(),
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  CashbookModel copyWith({
    String? id,
    String? userId,
    String? businessId,
    String? templateId,
    String? name,
    String? description,
    String? currency,
    String? color,
    String? icon,
    double? balance,
    int? transactionCount,
    bool? isDefault,
    bool? isActive,
    DateTime? lastActivity,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return CashbookModel(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      businessId: businessId ?? this.businessId,
      templateId: templateId ?? this.templateId,
      name: name ?? this.name,
      description: description ?? this.description,
      currency: currency ?? this.currency,
      color: color ?? this.color,
      icon: icon ?? this.icon,
      balance: balance ?? this.balance,
      transactionCount: transactionCount ?? this.transactionCount,
      isDefault: isDefault ?? this.isDefault,
      isActive: isActive ?? this.isActive,
      lastActivity: lastActivity ?? this.lastActivity,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }
}