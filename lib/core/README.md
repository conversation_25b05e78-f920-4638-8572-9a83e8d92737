# Flutter Cashbook - Core Services Documentation

This directory contains the core services and data layer for the Flutter Cashbook application, providing a well-structured interface to interact with <PERSON><PERSON><PERSON>.

## Architecture Overview

The service layer follows a clean architecture pattern with the following layers:

```
Presentation Layer (UI)
    ↓
Service Layer (Business Logic)
    ↓
Repository Layer (Data Access)
    ↓
Supabase (Database)
```

## Directory Structure

```
core/
├── models/                 # Data models
├── repositories/          # Data access layer
├── services/             # Business logic layer
└── README.md            # This documentation
```

## Models

### Core Models
- **UserModel**: User profile information
- **BusinessModel**: Business/organization data
- **CashbookModel**: Cashbook entities
- **TransactionModel**: Financial transactions
- **CategoryModel**: Transaction categories
- **PaymentModeModel**: Payment methods
- **CashbookTemplateModel**: Predefined cashbook templates

### Utility Models
- **ApiResponse<T>**: Generic API response wrapper
- **TransactionSummary**: Transaction analytics data
- **DashboardData**: Dashboard aggregated data

## Services

### AppConfigService
Handles application configuration and initialization.

```dart
// Initialize the app
await AppConfigService.instance.initialize();

// Get configuration values
final supabaseUrl = AppConfigService.instance.supabaseUrl;
final isDevelopment = AppConfigService.instance.isDevelopment;
```

### SupabaseService
Low-level Supabase client wrapper.

```dart
// Check authentication
final isAuth = SupabaseService.instance.isAuthenticated;

// Get current user
final user = SupabaseService.instance.currentUser;
```

### AuthService
High-level authentication operations.

```dart
final authService = AuthService.instance;

// Sign up
final user = await authService.signUp(
  email: '<EMAIL>',
  password: 'password123',
  fullName: 'John Doe',
);

// Sign in
final user = await authService.signIn(
  email: '<EMAIL>',
  password: 'password123',
);

// Sign out
await authService.signOut();
```

### DataService
High-level business operations combining multiple repositories.

```dart
final dataService = DataService.instance;

// Get dashboard data
final dashboardData = await dataService.getDashboardData();

// Create cashbook
final cashbook = await dataService.createCashbook(
  name: 'Personal Expenses',
  currency: 'USD',
);

// Create transaction
final transaction = await dataService.createTransaction(
  cashbookId: cashbook.id,
  type: TransactionType.expense,
  amount: 50.0,
  description: 'Grocery shopping',
  transactionDate: DateTime.now(),
);
```

## Repositories

Each repository handles CRUD operations for a specific entity:

- **UserRepository**: User profile operations
- **BusinessRepository**: Business management
- **CashbookRepository**: Cashbook operations
- **TransactionRepository**: Transaction management
- **CategoryRepository**: Category operations
- **PaymentModeRepository**: Payment method operations
- **CashbookTemplateRepository**: Template operations

### Example Repository Usage

```dart
final cashbookRepo = ServiceLocator.instance.cashbookRepository;

// Get user's cashbooks
final cashbooks = await cashbookRepo.getUserCashbooks();

// Create new cashbook
final newCashbook = await cashbookRepo.createCashbook(
  name: 'Business Expenses',
  currency: 'USD',
);

// Update cashbook
final updated = await cashbookRepo.updateCashbook(
  cashbookId: newCashbook.id,
  name: 'Updated Name',
);
```

## Setup and Initialization

### 1. Configuration File
Create `env.json` in the project root:

```json
{
  "SUPABASE_URL": "your_supabase_url",
  "SUPABASE_ANON_KEY": "your_supabase_anon_key",
  "ENVIRONMENT": "development",
  "APP_VERSION": "1.0.0"
}
```

### 2. App Initialization
In your `main.dart`:

```dart
void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  
  // Initialize app configuration
  await AppConfigService.instance.initialize();
  
  runApp(MyApp());
}

class MyApp extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: ServiceLocator.getProviders(),
      child: MaterialApp(
        // Your app configuration
      ),
    );
  }
}
```

### 3. Using in Widgets
```dart
class CashbookListWidget extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Consumer<DataService>(
      builder: (context, dataService, child) {
        return FutureBuilder<List<CashbookModel>>(
          future: dataService.getUserCashbooks(),
          builder: (context, snapshot) {
            if (snapshot.hasData) {
              return ListView.builder(
                itemCount: snapshot.data!.length,
                itemBuilder: (context, index) {
                  final cashbook = snapshot.data![index];
                  return ListTile(
                    title: Text(cashbook.name),
                    subtitle: Text('Balance: \$${cashbook.balance}'),
                  );
                },
              );
            }
            return CircularProgressIndicator();
          },
        );
      },
    );
  }
}
```

## Error Handling

All services and repositories include comprehensive error handling:

```dart
try {
  final result = await dataService.createTransaction(/* parameters */);
  // Handle success
} catch (error) {
  // Handle error - error messages are user-friendly
  print('Error: $error');
}
```

## Database Schema

The service layer is built on top of the v1.sql schema which includes:

- Row Level Security (RLS) policies
- Automatic balance updates via triggers
- Comprehensive indexing for performance
- Default templates and categories
- User preferences and settings

## Best Practices

1. **Always use DataService** for business operations instead of repositories directly
2. **Handle errors gracefully** - all methods can throw exceptions
3. **Use Provider/Consumer** for state management in widgets
4. **Initialize services** before using them
5. **Check authentication** before performing user-specific operations

## Testing

The service layer is designed to be easily testable:

```dart
// Mock repositories for testing
class MockUserRepository extends UserRepository {
  @override
  Future<UserModel?> getCurrentUser() async {
    return UserModel(/* test data */);
  }
}
```

## Performance Considerations

- Repositories include pagination support
- Queries are optimized with proper indexing
- Connection pooling is handled by Supabase
- Local caching can be implemented at the service layer

## Security

- All operations respect Row Level Security policies
- User data is automatically filtered by user ID
- Authentication is required for all user-specific operations
- Sensitive operations include additional validation

For more examples, see `example_usage.dart` in the services directory.